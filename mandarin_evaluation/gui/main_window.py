#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI主窗口模块
实现普通话评测系统的图形用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
from pathlib import Path
import sys
import os

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.audio_processor import AudioProcessor
from core.evaluator import MandarinEvaluator

class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.root.title("普通话评测系统 v1.0")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标和样式
        self.setup_styles()
        
        # 初始化组件
        self.audio_processor = AudioProcessor()
        self.evaluator = MandarinEvaluator()
        
        # 设置数据路径
        data_path = Path(__file__).parent.parent / "data"
        self.evaluator.set_data_path(str(data_path))
        
        self.current_text = ""
        self.current_text_id = ""
        self.current_audio_data = None
        self.recording_thread = None
        self.is_recording = False
        self.last_evaluation_result = None
        self.is_playing = False
        self.is_paused = False
        self.current_playing_audio = None
        self.audio_stream = None
        
        # 先创建界面组件
        self.create_widgets()
        
        # 再加载文本数据（GUI组件已存在）
        self.load_texts()
        
        # 绑定事件
        self.bind_events()
    
    def setup_styles(self):
        """设置界面样式"""
        try:
            self.root.tk.call('tk', 'scaling', 1.2)  # 提高DPI缩放
        except:
            pass
        
        # 设置颜色主题
        self.colors = {
            'primary': '#2E86AB',      # 主色调
            'secondary': '#A23B72',    # 辅助色
            'success': '#F18F01',      # 成功色
            'warning': '#C73E1D',      # 警告色
            'background': '#F5F5F5',   # 背景色
            'text': '#333333',         # 文字色
            'light_gray': '#E8E8E8',   # 浅灰色
        }
        
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义按钮样式
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none')
        
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none')
        
        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none')
    
    def load_texts(self):
        """加载文本数据"""
        try:
            texts_file = Path(__file__).parent.parent / "data" / "parsed_texts.json"
            if texts_file.exists():
                with open(texts_file, 'r', encoding='utf-8') as f:
                    self.texts_data = json.load(f)
                print(f"加载了 {len(self.texts_data)} 个文本")
                
                # 延迟更新下拉框选项，确保GUI组件已创建
                if hasattr(self, 'text_combobox'):
                    self.update_text_combobox()
                    
            else:
                self.texts_data = {}
                print("警告: 文本数据文件不存在")
        except Exception as e:
            print(f"加载文本数据失败: {e}")
            self.texts_data = {}
    
    def update_text_combobox(self):
        """更新文本下拉框选项"""
        if self.texts_data and hasattr(self, 'text_combobox'):
            options = [f"文本{k}: {v[:50]}..." if len(v) > 50 else f"文本{k}: {v}" 
                      for k, v in self.texts_data.items()]
            self.text_combobox['values'] = options
            if options:
                self.text_combobox.current(0)
                self.on_text_selection_changed()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 创建各个区域
        self.create_text_selection_area(main_frame)
        self.create_control_area(main_frame)
        self.create_text_display_area(main_frame)
        self.create_results_area(main_frame)
        self.create_status_area(main_frame)
    
    def create_text_selection_area(self, parent):
        """创建文本选择区域"""
        # 文本选择框架
        text_frame = ttk.LabelFrame(parent, text="选择朗读文本", padding="10")
        text_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        text_frame.columnconfigure(1, weight=1)
        
        # 文本选择下拉框
        ttk.Label(text_frame, text="选择文本:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.text_var = tk.StringVar()
        self.text_combobox = ttk.Combobox(text_frame, textvariable=self.text_var, 
                                         state="readonly", width=50)
        self.text_combobox.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 填充下拉框选项将在load_texts()中延迟处理
        
        # 绑定选择事件
        self.text_combobox.bind('<<ComboboxSelected>>', lambda e: self.on_text_selection_changed())
        
        # 刷新按钮
        ttk.Button(text_frame, text="刷新", 
                  command=self.refresh_texts).grid(row=0, column=2, sticky=tk.E)
    
    def create_control_area(self, parent):
        """创建控制区域"""
        # 控制按钮框架
        control_frame = ttk.LabelFrame(parent, text="录音控制", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮容器
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        # 录音按钮
        self.record_button = ttk.Button(button_frame, text="🎙️ 开始录音", 
                                       style='Success.TButton',
                                       command=self.toggle_recording)
        self.record_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放录音按钮
        self.play_button = ttk.Button(button_frame, text="▶️ 播放录音", 
                                     command=self.play_recording,
                                     state=tk.DISABLED)
        self.play_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放标准音频按钮
        self.play_standard_button = ttk.Button(button_frame, text="🔊 播放标准音频",
                                              command=self.play_standard_audio)
        self.play_standard_button.pack(side=tk.LEFT, padx=(0, 10))

        # 重新播放按钮
        self.replay_button = ttk.Button(button_frame, text="🔄 重新播放",
                                       command=self.replay_audio,
                                       state=tk.DISABLED)
        self.replay_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 评测按钮
        self.evaluate_button = ttk.Button(button_frame, text="📊 开始评测", 
                                         style='Primary.TButton',
                                         command=self.start_evaluation,
                                         state=tk.DISABLED)
        self.evaluate_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存录音按钮
        self.save_button = ttk.Button(button_frame, text="💾 保存录音", 
                                     command=self.save_recording,
                                     state=tk.DISABLED)
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 生成错误报告按钮
        self.error_report_button = ttk.Button(button_frame, text="📋 错误报告",
                                            command=self.generate_error_report,
                                            state=tk.DISABLED)
        self.error_report_button.pack(side=tk.LEFT, padx=(0, 10))

        # 加载音频文件按钮
        self.load_button = ttk.Button(button_frame, text="📁 加载音频",
                                     command=self.load_audio_file)
        self.load_button.pack(side=tk.RIGHT)
    
    def create_text_display_area(self, parent):
        """创建文本显示区域"""
        # 文本显示框架
        text_display_frame = ttk.LabelFrame(parent, text="朗读文本", padding="10")
        text_display_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        text_display_frame.columnconfigure(0, weight=1)
        text_display_frame.rowconfigure(0, weight=1)
        
        # 文本显示区域
        self.text_display = scrolledtext.ScrolledText(text_display_frame, 
                                                     wrap=tk.WORD, 
                                                     height=15,
                                                     font=('Microsoft YaHei', 12),
                                                     state=tk.DISABLED)
        self.text_display.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def create_results_area(self, parent):
        """创建结果显示区域"""
        # 结果显示框架
        results_frame = ttk.LabelFrame(parent, text="评测结果", padding="10")
        results_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(1, weight=1)
        
        # 评分概览
        scores_frame = ttk.Frame(results_frame)
        scores_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        scores_frame.columnconfigure((0, 1), weight=1)
        
        # 评分标签
        self.score_labels = {}
        score_items = [
            ('声母', 'consonant'),
            ('韵母', 'vowel'),
            ('声调', 'tone'),
            ('流畅性', 'fluency')
        ]
        
        for i, (name, key) in enumerate(score_items):
            row, col = divmod(i, 2)
            frame = ttk.Frame(scores_frame)
            frame.grid(row=row, column=col, sticky=(tk.W, tk.E), padx=5, pady=2)
            
            ttk.Label(frame, text=f"{name}:").pack(side=tk.LEFT)
            score_label = ttk.Label(frame, text="--", 
                                   foreground=self.colors['primary'],
                                   font=('Arial', 12, 'bold'))
            score_label.pack(side=tk.RIGHT)
            self.score_labels[key] = score_label
        
        # 详细结果
        self.results_display = scrolledtext.ScrolledText(results_frame, 
                                                        wrap=tk.WORD, 
                                                        height=12,
                                                        font=('Microsoft YaHei', 10),
                                                        state=tk.DISABLED)
        self.results_display.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def create_status_area(self, parent):
        """创建状态栏"""
        # 状态栏框架
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, text="就绪", 
                                     foreground=self.colors['text'])
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, 
                                          variable=self.progress_var,
                                          maximum=100,
                                          length=200)
        self.progress_bar.grid(row=0, column=1, sticky=tk.E, padx=(10, 0))
    
    def bind_events(self):
        """绑定事件"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 键盘快捷键
        self.root.bind('<Control-r>', lambda e: self.toggle_recording())
        self.root.bind('<Control-p>', lambda e: self.play_recording())
        self.root.bind('<Control-e>', lambda e: self.start_evaluation())
    
    def on_text_selection_changed(self):
        """文本选择改变事件"""
        selection = self.text_var.get()
        if selection and ':' in selection:
            text_id = selection.split(':')[0].replace('文本', '')
            if text_id in self.texts_data:
                self.current_text = self.texts_data[text_id]
                self.current_text_id = text_id
                self.update_text_display()
                self.update_status(f"已选择文本 {text_id}")
    
    def update_text_display(self):
        """更新文本显示"""
        # 添加安全检查，确保组件已存在
        if not hasattr(self, 'text_display'):
            return
        
        self.text_display.config(state=tk.NORMAL)
        self.text_display.delete(1.0, tk.END)
        self.text_display.insert(1.0, self.current_text)
        self.text_display.config(state=tk.DISABLED)
    
    def toggle_recording(self):
        """切换录音状态"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        """开始录音"""
        if not self.current_text:
            messagebox.showwarning("警告", "请先选择要朗读的文本！")
            return
        
        self.is_recording = True
        self.record_button.configure(text="⏹️ 停止录音", style='Warning.TButton')
        self.update_status("正在录音...")
        
        # 在后台线程中开始录音
        def record_audio():
            success = self.audio_processor.start_recording()
            if not success:
                self.root.after(0, lambda: self.update_status("录音开始失败！"))
                self.root.after(0, self.reset_recording_ui)
        
        self.recording_thread = threading.Thread(target=record_audio)
        self.recording_thread.start()
    
    def stop_recording(self):
        """停止录音"""
        self.is_recording = False
        self.record_button.configure(text="🎙️ 开始录音", style='Success.TButton')
        self.update_status("正在处理录音...")
        
        # 在后台线程中停止录音
        def stop_and_process():
            audio_data = self.audio_processor.stop_recording()
            if audio_data is not None:
                self.current_audio_data = audio_data
                self.root.after(0, self.on_recording_complete)
            else:
                self.root.after(0, lambda: self.update_status("录音处理失败！"))
        
        threading.Thread(target=stop_and_process).start()
    
    def on_recording_complete(self):
        """录音完成处理"""
        self.play_button.config(state=tk.NORMAL)
        self.evaluate_button.config(state=tk.NORMAL)
        self.save_button.config(state=tk.NORMAL)
        
        # 显示音频信息
        if self.current_audio_data is not None:
            info = self.audio_processor.get_audio_info(self.current_audio_data)
            self.update_status(f"录音完成，时长: {info['duration']:.2f}秒")
        else:
            self.update_status("录音完成")
    
    def reset_recording_ui(self):
        """重置录音UI状态"""
        self.is_recording = False
        self.record_button.configure(text="🎙️ 开始录音", style='Success.TButton')
    
    def play_recording(self):
        """播放录音"""
        if self.is_playing and not self.is_paused:
            # 当前正在播放，点击暂停
            self.is_paused = True
            self.audio_processor.pause_audio()
            self.play_button.configure(text="▶️ 继续播放")
            self.update_status("录音播放已暂停")
            return
        elif self.is_playing and self.is_paused:
            # 当前暂停，点击恢复
            self.is_paused = False
            self.audio_processor.resume_audio()
            self.play_button.configure(text="⏸️ 暂停播放")
            self.update_status("录音播放已恢复")
            return

        if self.current_audio_data is None:
            messagebox.showwarning("警告", "没有录音数据！")
            return

        self.is_playing = True
        self.is_paused = False
        self.play_button.configure(text="⏸️ 暂停播放")
        self.update_status("播放录音中...")

        def play_audio():
            try:
                # 播放录音
                success = self.audio_processor.play_audio(self.current_audio_data)
                if success:
                    self.root.after(0, lambda: self.update_status("录音播放完成"))
                else:
                    self.root.after(0, lambda: self.update_status("录音播放失败"))
            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"播放失败: {str(e)}"))
            finally:
                # 播放完成后重置状态
                self.root.after(0, self.reset_recording_play_ui)

        # 在后台线程播放音频
        threading.Thread(target=play_audio, daemon=True).start()

    def reset_recording_play_ui(self):
        """重置录音播放UI状态"""
        self.is_playing = False
        self.is_paused = False
        self.play_button.configure(text="▶️ 播放录音")
    
    def play_standard_audio(self):
        """播放/暂停/恢复标准音频"""
        if self.is_playing and not self.is_paused:
            # 当前正在播放，点击暂停
            self.is_paused = True
            self.audio_processor.pause_audio()
            self.play_standard_button.configure(text="▶️ 继续播放")
            self.update_status("音频播放已暂停")
            return
        elif self.is_playing and self.is_paused:
            # 当前暂停，点击恢复
            self.is_paused = False
            self.audio_processor.resume_audio()
            self.play_standard_button.configure(text="⏸️ 暂停播放")
            self.update_status("音频播放已恢复")
            return

        # 开始新的播放
        selection = self.text_var.get()
        if not selection:
            messagebox.showwarning("警告", "请先选择文本！")
            return

        text_id = selection.split(':')[0].replace('文本', '')
        audio_path = Path(__file__).parent.parent / "data" / f"{text_id.zfill(2)}.MP3"

        if audio_path.exists():
            self.is_playing = True
            self.is_paused = False
            self.play_standard_button.configure(text="⏸️ 暂停播放")
            self.replay_button.config(state=tk.NORMAL)
            self.update_status("播放标准音频中...")

            def play_audio():
                try:
                    # 加载音频文件
                    result = self.audio_processor.load_audio(str(audio_path))
                    if result:
                        audio_data, sr = result
                        self.current_playing_audio = audio_data
                        # 播放音频
                        success = self.audio_processor.play_audio(audio_data)
                        if success:
                            self.root.after(0, lambda: self.update_status("标准音频播放完成"))
                        else:
                            self.root.after(0, lambda: self.update_status("音频播放失败"))
                    else:
                        self.root.after(0, lambda: self.update_status("音频加载失败"))
                except Exception as e:
                    self.root.after(0, lambda: self.update_status(f"播放失败: {str(e)}"))
                finally:
                    # 播放完成后重置状态
                    self.root.after(0, self.reset_audio_ui)

            # 在后台线程播放音频
            threading.Thread(target=play_audio, daemon=True).start()
        else:
            messagebox.showerror("错误", f"标准音频文件不存在: {audio_path}")

    def replay_audio(self):
        """重新播放音频"""
        if self.current_playing_audio is not None:
            # 停止当前播放
            self.audio_processor.stop_audio()

            # 重置状态
            self.is_playing = True
            self.is_paused = False
            self.play_standard_button.configure(text="⏸️ 暂停播放")
            self.update_status("重新播放音频中...")

            def play_audio():
                try:
                    # 播放音频
                    success = self.audio_processor.play_audio(self.current_playing_audio)
                    if success:
                        self.root.after(0, lambda: self.update_status("音频播放完成"))
                    else:
                        self.root.after(0, lambda: self.update_status("音频播放失败"))
                except Exception as e:
                    self.root.after(0, lambda: self.update_status(f"播放失败: {str(e)}"))
                finally:
                    # 播放完成后重置状态
                    self.root.after(0, self.reset_audio_ui)

            # 在后台线程播放音频
            threading.Thread(target=play_audio, daemon=True).start()

    def reset_audio_ui(self):
        """重置音频播放UI状态"""
        self.is_playing = False
        self.is_paused = False
        self.play_standard_button.configure(text="🔊 播放标准音频")
        self.replay_button.config(state=tk.DISABLED)
    
    def start_evaluation(self):
        """开始评测"""
        if self.current_audio_data is None:
            messagebox.showwarning("警告", "请先录音或加载音频文件！")
            return
        
        if not self.current_text_id:
            messagebox.showwarning("警告", "请先选择要评测的文本！")
            return
        
        self.update_status("正在评测...")
        self.progress_var.set(0)
        
        # 禁用评测按钮
        self.evaluate_button.config(state=tk.DISABLED)
        
        # 在后台线程中进行评测
        def evaluate():
            try:
                # 进度回调函数
                def progress_callback(message):
                    self.root.after(0, lambda: self.update_status(f"评测中: {message}"))
                    # 模拟进度更新
                    current_progress = self.progress_var.get()
                    new_progress = min(current_progress + 5, 95)
                    self.root.after(0, lambda: self.progress_var.set(new_progress))
                
                # 调用评测引擎
                evaluation_result = self.evaluator.evaluate_complete(
                    self.current_audio_data, 
                    self.current_text_id, 
                    progress_callback
                )
                
                # 更新进度到100%
                self.root.after(0, lambda: self.progress_var.set(100))
                
                if 'error' not in evaluation_result:
                    # 转换结果格式以适配显示函数
                    dimension_scores = evaluation_result['dimension_scores']
                    display_results = {
                        'consonant': dimension_scores['consonant'],
                        'vowel': dimension_scores['vowel'],
                        'tone': dimension_scores['tone'],
                        'fluency': dimension_scores['fluency'],
                        'total_score': evaluation_result['total_score'],
                        'grade': evaluation_result['grade'],
                        'details': evaluation_result['feedback']
                    }

                    # 保存完整的评测结果用于生成错误报告
                    self.last_evaluation_result = evaluation_result

                    self.root.after(0, lambda: self.display_results(display_results))
                else:
                    self.root.after(0, lambda: self.update_status(f"评测失败: {evaluation_result['error']}"))
                    self.root.after(0, lambda: messagebox.showerror("评测失败", evaluation_result['error']))
                
            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"评测失败: {e}"))
                self.root.after(0, lambda: messagebox.showerror("评测失败", str(e)))
            finally:
                # 重新启用评测按钮
                self.root.after(0, lambda: self.evaluate_button.config(state=tk.NORMAL))
                self.root.after(2000, lambda: self.progress_var.set(0))
        
        threading.Thread(target=evaluate, daemon=True).start()
    
    def display_results(self, results):
        """显示评测结果"""
        # 更新评分显示
        for key, score in results.items():
            if key in self.score_labels:
                self.score_labels[key].configure(text=f"{score:.1f}%")
                
                # 根据分数设置颜色
                if score >= 90:
                    color = self.colors['success']
                elif score >= 80:
                    color = self.colors['primary']
                else:
                    color = self.colors['warning']
                self.score_labels[key].configure(foreground=color)
        
        # 构建详细结果文本
        details_text = "=== 普通话评测结果 ===\n\n"
        
        # 总分和等级
        if 'total_score' in results:
            details_text += f"🎯 总分: {results['total_score']:.1f}分\n"
        if 'grade' in results:
            details_text += f"🏆 等级: {results['grade']}\n\n"
        
        # 各维度得分
        details_text += "📊 各维度得分:\n"
        dimension_names = {
            'consonant': '声母',
            'vowel': '韵母', 
            'tone': '声调',
            'fluency': '流畅性'
        }
        
        for key in ['consonant', 'vowel', 'tone', 'fluency']:
            if key in results:
                name = dimension_names[key]
                score = results[key]
                details_text += f"  • {name}: {score:.1f}分\n"
        
        details_text += "\n"
        
        # 详细反馈
        if 'details' in results:
            details_text += "💬 详细反馈:\n"
            details_text += results['details']
        
        # 显示结果
        self.results_display.config(state=tk.NORMAL)
        self.results_display.delete(1.0, tk.END)
        self.results_display.insert(1.0, details_text)
        self.results_display.config(state=tk.DISABLED)
        
        # 启用错误报告按钮
        self.error_report_button.config(state=tk.NORMAL)

        # 更新状态
        total_score = results.get('total_score', 0)
        self.update_status(f"评测完成！总分: {total_score:.1f}分")

    def generate_error_report(self):
        """生成错误报告"""
        if self.last_evaluation_result is None:
            messagebox.showwarning("警告", "没有评测结果，请先进行评测！")
            return

        try:
            # 生成错误报告
            error_report = self.evaluator.generate_error_report(self.last_evaluation_result)

            # 创建新窗口显示报告
            report_window = tk.Toplevel(self.root)
            report_window.title("普通话评测错误报告")
            report_window.geometry("800x600")
            report_window.resizable(True, True)

            # 创建文本显示区域
            report_frame = ttk.Frame(report_window, padding="10")
            report_frame.pack(fill=tk.BOTH, expand=True)

            # 报告显示区域
            report_text = scrolledtext.ScrolledText(
                report_frame,
                wrap=tk.WORD,
                font=('Consolas', 10),
                state=tk.NORMAL
            )
            report_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # 插入报告内容
            report_text.insert(1.0, error_report)
            report_text.config(state=tk.DISABLED)

            # 按钮框架
            button_frame = ttk.Frame(report_frame)
            button_frame.pack(fill=tk.X)

            # 保存报告按钮
            def save_report():
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".txt",
                    filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                    title="保存错误报告"
                )
                if file_path:
                    try:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(error_report)
                        messagebox.showinfo("成功", f"错误报告已保存到: {file_path}")
                    except Exception as e:
                        messagebox.showerror("错误", f"保存失败: {str(e)}")

            save_report_button = ttk.Button(button_frame, text="💾 保存报告", command=save_report)
            save_report_button.pack(side=tk.LEFT)

            # 关闭按钮
            close_button = ttk.Button(button_frame, text="❌ 关闭", command=report_window.destroy)
            close_button.pack(side=tk.RIGHT)

            self.update_status("错误报告已生成")

        except Exception as e:
            messagebox.showerror("错误", f"生成错误报告失败: {str(e)}")
            self.update_status(f"生成错误报告失败: {str(e)}")

    def save_recording(self):
        """保存录音"""
        if self.current_audio_data is None:
            messagebox.showwarning("警告", "没有录音数据！")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".wav",
            filetypes=[("WAV files", "*.wav"), ("All files", "*.*")]
        )
        
        if file_path:
            success = self.audio_processor.save_audio(self.current_audio_data, file_path)
            if success:
                messagebox.showinfo("成功", f"录音已保存到: {file_path}")
                self.update_status(f"录音已保存: {Path(file_path).name}")
            else:
                messagebox.showerror("错误", "保存录音失败！")
    
    def load_audio_file(self):
        """加载音频文件"""
        file_path = filedialog.askopenfilename(
            filetypes=[
                ("Audio files", "*.wav *.mp3 *.m4a *.flac"),
                ("WAV files", "*.wav"),
                ("MP3 files", "*.mp3"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.update_status("正在加载音频文件...")
            
            def load_audio():
                result = self.audio_processor.load_audio(file_path)
                if result:
                    audio_data, sr = result
                    self.current_audio_data = audio_data
                    self.root.after(0, self.on_audio_loaded)
                    self.root.after(0, lambda: self.update_status(f"音频文件已加载: {Path(file_path).name}"))
                else:
                    self.root.after(0, lambda: self.update_status("音频文件加载失败！"))
            
            threading.Thread(target=load_audio).start()
    
    def on_audio_loaded(self):
        """音频加载完成处理"""
        self.play_button.config(state=tk.NORMAL)
        self.evaluate_button.config(state=tk.NORMAL)
        self.save_button.config(state=tk.NORMAL)
    
    def refresh_texts(self):
        """刷新文本列表"""
        self.load_texts()
        if self.texts_data:
            options = [f"文本{k}: {v[:50]}..." if len(v) > 50 else f"文本{k}: {v}" 
                      for k, v in self.texts_data.items()]
            self.text_combobox['values'] = options
            self.update_status("文本列表已刷新")
        else:
            self.update_status("没有找到文本数据")
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()
    
    def on_closing(self):
        """窗口关闭事件处理"""
        if self.is_recording:
            if messagebox.askokcancel("确认", "正在录音中，确定要退出吗？"):
                self.audio_processor.stop_recording()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """运行主循环"""
        try:
            # 显示启动信息
            self.update_status("普通话评测系统已启动")
            
            # 启动主循环
            self.root.mainloop()
            
        except Exception as e:
            messagebox.showerror("错误", f"程序运行出错: {e}")

# 测试代码
if __name__ == "__main__":
    app = MainWindow()
    app.run()