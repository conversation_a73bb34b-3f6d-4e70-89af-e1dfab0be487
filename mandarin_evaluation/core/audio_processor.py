#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频处理模块
处理音频录制、播放、特征提取等基础功能
"""

import numpy as np
import librosa
import soundfile as sf
import pyaudio
import threading
import time
from pathlib import Path
from typing import Optional, Tuple, Dict, Any

class AudioProcessor:
    """音频处理器"""
    
    def __init__(self, sample_rate: int = 16000, chunk_size: int = 1024):
        """
        初始化音频处理器
        
        Args:
            sample_rate: 采样率，默认16kHz
            chunk_size: 音频块大小
        """
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 初始化PyAudio
        self.audio = pyaudio.PyAudio()
        
        # 录音相关属性
        self.is_recording = False
        self.recorded_frames = []
        self.stream = None
        
    def __del__(self):
        """析构函数，清理资源"""
        if hasattr(self, 'audio'):
            self.audio.terminate()
    
    def start_recording(self) -> bool:
        """
        开始录音
        
        Returns:
            bool: 录音是否成功开始
        """
        try:
            self.recorded_frames = []
            self.is_recording = True
            
            # 打开音频流
            self.stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size,
                stream_callback=self._recording_callback
            )
            
            self.stream.start_stream()
            print("开始录音...")
            return True
            
        except Exception as e:
            print(f"录音开始失败: {e}")
            self.is_recording = False
            return False
    
    def stop_recording(self) -> Optional[np.ndarray]:
        """
        停止录音并返回音频数据
        
        Returns:
            np.ndarray: 录音的音频数据，如果失败返回None
        """
        if not self.is_recording:
            return None
            
        try:
            self.is_recording = False
            
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            
            if not self.recorded_frames:
                print("没有录制到音频数据")
                return None
            
            # 将录音数据转换为numpy数组
            audio_data = np.frombuffer(b''.join(self.recorded_frames), dtype=np.int16)
            audio_data = audio_data.astype(np.float32) / 32768.0  # 归一化到[-1, 1]
            
            print(f"录音完成，时长: {len(audio_data)/self.sample_rate:.2f}秒")
            return audio_data
            
        except Exception as e:
            print(f"录音停止失败: {e}")
            return None
    
    def _recording_callback(self, in_data, frame_count, time_info, status):
        """录音回调函数"""
        if self.is_recording:
            self.recorded_frames.append(in_data)
        return (None, pyaudio.paContinue)
    
    def load_audio(self, file_path: str) -> Optional[Tuple[np.ndarray, int]]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Tuple[np.ndarray, int]: (音频数据, 采样率)，失败返回None
        """
        try:
            audio_data, sr = librosa.load(file_path, sr=self.sample_rate)
            print(f"加载音频文件: {file_path}, 时长: {len(audio_data)/sr:.2f}秒")
            return audio_data, sr
        except Exception as e:
            print(f"加载音频文件失败: {e}")
            return None
    
    def save_audio(self, audio_data: np.ndarray, file_path: str) -> bool:
        """
        保存音频文件
        
        Args:
            audio_data: 音频数据
            file_path: 保存路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            sf.write(file_path, audio_data, self.sample_rate)
            print(f"音频文件已保存: {file_path}")
            return True
        except Exception as e:
            print(f"保存音频文件失败: {e}")
            return False
    
    def extract_features(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        提取音频特征
        
        Args:
            audio_data: 音频数据
            
        Returns:
            Dict[str, Any]: 包含各种音频特征的字典
        """
        try:
            features = {}
            
            # 1. MFCC特征
            mfcc = librosa.feature.mfcc(
                y=audio_data, 
                sr=self.sample_rate, 
                n_mfcc=13
            )
            features['mfcc'] = mfcc
            
            # 2. 梅尔频谱
            mel_spectrogram = librosa.feature.melspectrogram(
                y=audio_data, 
                sr=self.sample_rate
            )
            features['mel_spectrogram'] = mel_spectrogram
            
            # 3. 色度特征
            chroma = librosa.feature.chroma_stft(
                y=audio_data, 
                sr=self.sample_rate
            )
            features['chroma'] = chroma
            
            # 4. 基频F0
            f0, voiced_flag, voiced_probs = librosa.pyin(
                audio_data,
                fmin=librosa.note_to_hz('C2'),
                fmax=librosa.note_to_hz('C7')
            )
            features['f0'] = f0
            features['voiced_flag'] = voiced_flag
            features['voiced_probs'] = voiced_probs
            
            # 5. 短时傅里叶变换
            stft = librosa.stft(audio_data)
            features['stft'] = stft
            
            # 6. 频谱质心
            spectral_centroids = librosa.feature.spectral_centroid(
                y=audio_data, 
                sr=self.sample_rate
            )[0]
            features['spectral_centroids'] = spectral_centroids
            
            # 7. 零交叉率
            zcr = librosa.feature.zero_crossing_rate(audio_data)[0]
            features['zcr'] = zcr
            
            print(f"特征提取完成，包含 {len(features)} 种特征")
            return features
            
        except Exception as e:
            print(f"特征提取失败: {e}")
            return {}
    
    def detect_silence(self, audio_data: np.ndarray, 
                      threshold: float = 0.01, 
                      min_silence_len: float = 0.5) -> list:
        """
        检测音频中的静音段
        
        Args:
            audio_data: 音频数据
            threshold: 静音阈值
            min_silence_len: 最小静音长度(秒)
            
        Returns:
            list: 静音段的时间区间列表 [(start, end), ...]
        """
        try:
            # 计算音频的RMS能量
            frame_length = int(0.025 * self.sample_rate)  # 25ms窗口
            hop_length = int(0.01 * self.sample_rate)     # 10ms步长
            
            rms = librosa.feature.rms(
                y=audio_data,
                frame_length=frame_length,
                hop_length=hop_length
            )[0]
            
            # 时间轴
            times = librosa.frames_to_time(
                np.arange(len(rms)),
                sr=self.sample_rate,
                hop_length=hop_length
            )
            
            # 检测静音段
            silence_mask = rms < threshold
            silence_segments = []
            
            in_silence = False
            silence_start = 0
            
            for i, is_silent in enumerate(silence_mask):
                if is_silent and not in_silence:
                    # 开始静音段
                    silence_start = times[i]
                    in_silence = True
                elif not is_silent and in_silence:
                    # 结束静音段
                    silence_end = times[i]
                    if silence_end - silence_start >= min_silence_len:
                        silence_segments.append((silence_start, silence_end))
                    in_silence = False
            
            # 处理结尾的静音段
            if in_silence:
                silence_end = times[-1]
                if silence_end - silence_start >= min_silence_len:
                    silence_segments.append((silence_start, silence_end))
            
            return silence_segments
            
        except Exception as e:
            print(f"静音检测失败: {e}")
            return []
    
    def get_audio_info(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        获取音频基本信息
        
        Args:
            audio_data: 音频数据
            
        Returns:
            Dict[str, Any]: 音频信息字典
        """
        info = {
            'duration': len(audio_data) / self.sample_rate,
            'sample_rate': self.sample_rate,
            'length': len(audio_data),
            'max_amplitude': np.max(np.abs(audio_data)),
            'rms_energy': np.sqrt(np.mean(audio_data**2))
        }
        
        return info

# 测试代码
if __name__ == "__main__":
    processor = AudioProcessor()
    
    # 测试加载音频
    test_audio_path = "/mnt/e/Code/Outsourcing/普通话测评/mandarin_evaluation/data/音频/01.MP3"
    result = processor.load_audio(test_audio_path)
    
    if result:
        audio_data, sr = result
        print(f"音频信息: {processor.get_audio_info(audio_data)}")
        
        # 测试特征提取
        features = processor.extract_features(audio_data)
        print(f"提取到的特征: {list(features.keys())}")
        
        # 测试静音检测
        silence_segments = processor.detect_silence(audio_data)
        print(f"检测到 {len(silence_segments)} 个静音段")