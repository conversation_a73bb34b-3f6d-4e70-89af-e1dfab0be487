#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
声调评测模块
基于基频轮廓分析实现声调准确度评测
"""

import numpy as np
import librosa
from scipy import signal, interpolate
from scipy.stats import pearsonr
from scipy.spatial.distance import euclidean
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

class ToneEvaluator:
    """声调评测器"""
    
    def __init__(self, sample_rate: int = 16000):
        """
        初始化声调评测器
        
        Args:
            sample_rate: 采样率
        """
        self.sample_rate = sample_rate
        self.hop_length = int(0.01 * sample_rate)  # 10ms步长
        
        # 初始化声调模板
        self.initialize_tone_templates()
    
    def initialize_tone_templates(self):
        """初始化声调轮廓模板"""
        # 标准化的声调轮廓模板 (相对值，0-1之间)
        self.tone_templates = {
            1: {  # 第一声 - 高平调
                'contour': np.array([0.85, 0.85, 0.85, 0.85, 0.85]),
                'name': '第一声(阴平)',
                'description': '高平调，音高保持在较高水平',
                'characteristics': {'slope': 0, 'height': 'high', 'variation': 'minimal'}
            },
            2: {  # 第二声 - 高升调
                'contour': np.array([0.3, 0.45, 0.6, 0.75, 0.9]),
                'name': '第二声(阳平)', 
                'description': '高升调，从中音升到高音',
                'characteristics': {'slope': 'rising', 'height': 'mid_to_high', 'variation': 'rising'}
            },
            3: {  # 第三声 - 降升调
                'contour': np.array([0.5, 0.3, 0.2, 0.35, 0.55]),
                'name': '第三声(上声)',
                'description': '降升调，先降后升，形成V形',
                'characteristics': {'slope': 'falling_rising', 'height': 'mid', 'variation': 'V_shaped'}
            },
            4: {  # 第四声 - 高降调
                'contour': np.array([0.9, 0.7, 0.5, 0.3, 0.1]),
                'name': '第四声(去声)',
                'description': '高降调，从高音快速降到低音',
                'characteristics': {'slope': 'falling', 'height': 'high_to_low', 'variation': 'decreasing'}
            },
            0: {  # 轻声
                'contour': np.array([0.4, 0.4, 0.4, 0.4, 0.4]),
                'name': '轻声',
                'description': '轻声，音高较低且平稳',
                'characteristics': {'slope': 0, 'height': 'low', 'variation': 'minimal'}
            }
        }
        
        # 声调转换规律（用于连读变调）
        self.tone_change_rules = {
            (3, 3): (2, 3),  # 两个三声相连，前字变二声
            (1, 4): (1, 4),  # 一声+四声，不变
            (4, 1): (4, 1),  # 四声+一声，不变
        }
    
    def extract_f0_contour(self, audio_data: np.ndarray, 
                          start_time: float = 0, 
                          end_time: Optional[float] = None) -> Dict[str, Any]:
        """
        提取基频轮廓
        
        Args:
            audio_data: 音频数据
            start_time: 开始时间（秒）
            end_time: 结束时间（秒）
            
        Returns:
            Dict[str, Any]: 包含基频信息的字典
        """
        try:
            # 计算时间索引
            start_idx = int(start_time * self.sample_rate)
            end_idx = int(end_time * self.sample_rate) if end_time else len(audio_data)
            
            # 提取音频片段
            segment = audio_data[start_idx:end_idx]
            
            if len(segment) < self.sample_rate * 0.1:  # 至少100ms
                return {'error': '音频片段太短'}
            
            # 使用librosa的pyin算法提取基频
            try:
                # 尝试新版本的参数
                f0, voiced_flag, voiced_probs = librosa.pyin(
                    segment,
                    fmin=80,    # 最低频率80Hz
                    fmax=400,   # 最高频率400Hz
                    sr=self.sample_rate,
                    hop_length=self.hop_length,
                    pad_mode='constant'
                )
            except TypeError:
                # 如果失败，尝试旧版本的参数
                try:
                    f0, voiced_flag, voiced_probs = librosa.pyin(
                        segment,
                        fmin=80,
                        fmax=400,
                        sr=self.sample_rate,
                        hop_length=self.hop_length,
                        threshold=0.1,
                        pad_mode='constant'
                    )
                except:
                    # 最简单的调用方式
                    f0, voiced_flag, voiced_probs = librosa.pyin(
                        segment,
                        fmin=80,
                        fmax=400,
                        sr=self.sample_rate
                    )
            
            # 时间轴
            times = librosa.frames_to_time(
                np.arange(len(f0)),
                sr=self.sample_rate,
                hop_length=self.hop_length
            )
            
            # 清理基频数据
            clean_f0 = self.clean_f0_contour(f0, voiced_flag, voiced_probs)
            
            # 计算基频统计信息
            valid_f0 = clean_f0[~np.isnan(clean_f0)]
            
            if len(valid_f0) == 0:
                return {'error': '无法提取有效基频'}
            
            result = {
                'f0': clean_f0,
                'times': times,
                'voiced_flag': voiced_flag,
                'voiced_probs': voiced_probs,
                'statistics': {
                    'mean_f0': np.mean(valid_f0),
                    'std_f0': np.std(valid_f0),
                    'min_f0': np.min(valid_f0),
                    'max_f0': np.max(valid_f0),
                    'voiced_ratio': np.sum(voiced_flag) / len(voiced_flag),
                    'f0_range': np.max(valid_f0) - np.min(valid_f0)
                }
            }
            
            return result
            
        except Exception as e:
            print(f"基频提取失败: {e}")
            return {'error': str(e)}
    
    def clean_f0_contour(self, f0: np.ndarray, 
                        voiced_flag: np.ndarray, 
                        voiced_probs: np.ndarray,
                        confidence_threshold: float = 0.9) -> np.ndarray:
        """
        清理和平滑基频轮廓
        
        Args:
            f0: 原始基频数据
            voiced_flag: 语音标记
            voiced_probs: 语音概率
            confidence_threshold: 置信度阈值
            
        Returns:
            np.ndarray: 清理后的基频数据
        """
        try:
            clean_f0 = f0.copy()
            
            # 1. 移除低置信度的基频点
            low_confidence = voiced_probs < confidence_threshold
            clean_f0[low_confidence] = np.nan
            
            # 2. 移除明显的异常值（基于z-score）
            valid_indices = ~np.isnan(clean_f0)
            if np.sum(valid_indices) > 5:
                valid_f0 = clean_f0[valid_indices]
                z_scores = np.abs((valid_f0 - np.mean(valid_f0)) / np.std(valid_f0))
                outliers = z_scores > 2.5
                
                # 将异常值设为NaN
                valid_idx_array = np.where(valid_indices)[0]
                clean_f0[valid_idx_array[outliers]] = np.nan
            
            # 3. 线性插值填补短暂的空缺
            clean_f0 = self.interpolate_f0_gaps(clean_f0, max_gap=5)
            
            # 4. 平滑处理
            clean_f0 = self.smooth_f0_contour(clean_f0)
            
            return clean_f0
            
        except Exception as e:
            print(f"基频清理失败: {e}")
            return f0
    
    def interpolate_f0_gaps(self, f0: np.ndarray, max_gap: int = 5) -> np.ndarray:
        """
        插值填补基频空缺
        
        Args:
            f0: 基频数据
            max_gap: 最大插值间隔
            
        Returns:
            np.ndarray: 插值后的基频数据
        """
        try:
            result = f0.copy()
            valid_mask = ~np.isnan(result)
            
            if np.sum(valid_mask) < 2:
                return result
            
            # 找到所有的间隙
            gap_starts = []
            gap_ends = []
            in_gap = False
            
            for i in range(len(valid_mask)):
                if not valid_mask[i] and not in_gap:
                    gap_starts.append(i)
                    in_gap = True
                elif valid_mask[i] and in_gap:
                    gap_ends.append(i)
                    in_gap = False
            
            # 处理结尾的间隙
            if in_gap:
                gap_ends.append(len(valid_mask))
            
            # 插值填补小间隙
            for start, end in zip(gap_starts, gap_ends):
                gap_length = end - start
                if gap_length <= max_gap:
                    # 找到间隙前后的有效值
                    before_idx = start - 1 if start > 0 else None
                    after_idx = end if end < len(result) else None
                    
                    if before_idx is not None and after_idx is not None:
                        if valid_mask[before_idx] and valid_mask[after_idx]:
                            # 线性插值
                            before_val = result[before_idx]
                            after_val = result[after_idx]
                            interp_vals = np.linspace(before_val, after_val, gap_length + 2)[1:-1]
                            result[start:end] = interp_vals
            
            return result
            
        except Exception as e:
            print(f"基频插值失败: {e}")
            return f0
    
    def smooth_f0_contour(self, f0: np.ndarray, window_size: int = 3) -> np.ndarray:
        """
        平滑基频轮廓
        
        Args:
            f0: 基频数据
            window_size: 平滑窗口大小
            
        Returns:
            np.ndarray: 平滑后的基频数据
        """
        try:
            if window_size < 3:
                return f0
            
            result = f0.copy()
            valid_mask = ~np.isnan(result)
            
            if np.sum(valid_mask) < window_size:
                return result
            
            # 使用移动平均平滑
            for i in range(len(result)):
                if valid_mask[i]:
                    # 确定窗口范围
                    start = max(0, i - window_size // 2)
                    end = min(len(result), i + window_size // 2 + 1)
                    
                    # 只使用有效值计算平均
                    window_values = result[start:end]
                    window_valid = valid_mask[start:end]
                    
                    if np.sum(window_valid) > 0:
                        result[i] = np.mean(window_values[window_valid])
            
            return result
            
        except Exception as e:
            print(f"基频平滑失败: {e}")
            return f0
    
    def normalize_f0_contour(self, f0: np.ndarray, method: str = 'minmax') -> np.ndarray:
        """
        标准化基频轮廓
        
        Args:
            f0: 基频数据
            method: 标准化方法 ('minmax', 'zscore', 'log')
            
        Returns:
            np.ndarray: 标准化后的基频数据
        """
        try:
            valid_f0 = f0[~np.isnan(f0)]
            
            if len(valid_f0) == 0:
                return f0
            
            result = f0.copy()
            
            if method == 'minmax':
                # 最小-最大标准化
                min_f0 = np.min(valid_f0)
                max_f0 = np.max(valid_f0)
                if max_f0 > min_f0:
                    valid_mask = ~np.isnan(result)
                    result[valid_mask] = (result[valid_mask] - min_f0) / (max_f0 - min_f0)
            
            elif method == 'zscore':
                # Z-score标准化
                mean_f0 = np.mean(valid_f0)
                std_f0 = np.std(valid_f0)
                if std_f0 > 0:
                    valid_mask = ~np.isnan(result)
                    result[valid_mask] = (result[valid_mask] - mean_f0) / std_f0
            
            elif method == 'log':
                # 对数标准化
                valid_mask = ~np.isnan(result) & (result > 0)
                if np.sum(valid_mask) > 0:
                    log_f0 = np.log(result[valid_mask])
                    min_log = np.min(log_f0)
                    max_log = np.max(log_f0)
                    if max_log > min_log:
                        result[valid_mask] = (log_f0 - min_log) / (max_log - min_log)
            
            return result
            
        except Exception as e:
            print(f"基频标准化失败: {e}")
            return f0
    
    def resample_contour(self, contour: np.ndarray, target_length: int = 5) -> np.ndarray:
        """
        重采样轮廓到指定长度
        
        Args:
            contour: 原始轮廓
            target_length: 目标长度
            
        Returns:
            np.ndarray: 重采样后的轮廓
        """
        try:
            valid_mask = ~np.isnan(contour)
            if np.sum(valid_mask) < 2:
                return np.full(target_length, np.nan)
            
            # 获取有效数据点
            valid_indices = np.where(valid_mask)[0]
            valid_values = contour[valid_mask]
            
            # 创建插值函数
            if len(valid_values) < 2:
                return np.full(target_length, valid_values[0] if len(valid_values) > 0 else np.nan)
            
            interp_func = interpolate.interp1d(
                valid_indices, valid_values,
                kind='linear' if len(valid_values) >= 2 else 'nearest',
                bounds_error=False,
                fill_value='extrapolate'
            )
            
            # 生成目标采样点
            original_length = len(contour)
            target_indices = np.linspace(0, original_length - 1, target_length)
            
            # 插值
            resampled = interp_func(target_indices)
            
            return resampled
            
        except Exception as e:
            print(f"轮廓重采样失败: {e}")
            return np.full(target_length, np.nan)
    
    def evaluate_tone(self, user_audio: np.ndarray,
                     standard_audio: np.ndarray,
                     target_tone: int,
                     syllable_duration: float = 0.8) -> Dict[str, Any]:
        """
        评测声调
        
        Args:
            user_audio: 用户录音
            standard_audio: 标准录音
            target_tone: 目标声调 (0-4)
            syllable_duration: 音节持续时间
            
        Returns:
            Dict[str, Any]: 声调评测结果
        """
        try:
            # 提取用户音频的基频轮廓
            user_f0_data = self.extract_f0_contour(user_audio, 0, syllable_duration)
            if 'error' in user_f0_data:
                return {'score': 0, 'error': f"用户音频基频提取失败: {user_f0_data['error']}"}
            
            # 提取标准音频的基频轮廓
            standard_f0_data = self.extract_f0_contour(standard_audio, 0, syllable_duration)
            if 'error' in standard_f0_data:
                return {'score': 0, 'error': f"标准音频基频提取失败: {standard_f0_data['error']}"}
            
            # 标准化基频轮廓
            user_f0_norm = self.normalize_f0_contour(user_f0_data['f0'], 'minmax')
            standard_f0_norm = self.normalize_f0_contour(standard_f0_data['f0'], 'minmax')
            
            # 重采样到固定长度
            user_contour = self.resample_contour(user_f0_norm, 5)
            standard_contour = self.resample_contour(standard_f0_norm, 5)
            
            # 获取目标声调模板
            if target_tone not in self.tone_templates:
                return {'score': 0, 'error': f"未知声调: {target_tone}"}
            
            tone_template = self.tone_templates[target_tone]['contour']
            
            # 计算各种相似度指标
            similarity_scores = {}
            
            # 1. 与标准录音的相似度
            if not np.any(np.isnan(user_contour)) and not np.any(np.isnan(standard_contour)):
                # 皮尔逊相关系数
                corr, _ = pearsonr(user_contour, standard_contour)
                similarity_scores['correlation_standard'] = max(0, corr)
                
                # 欧几里得距离
                distance = euclidean(user_contour, standard_contour)
                similarity_scores['distance_standard'] = max(0, 1 - distance / np.sqrt(len(user_contour)))
            
            # 2. 与声调模板的相似度
            if not np.any(np.isnan(user_contour)):
                # 皮尔逊相关系数
                corr_template, _ = pearsonr(user_contour, tone_template)
                similarity_scores['correlation_template'] = max(0, corr_template)
                
                # 欧几里得距离
                distance_template = euclidean(user_contour, tone_template)
                similarity_scores['distance_template'] = max(0, 1 - distance_template / np.sqrt(len(user_contour)))
            
            # 3. 轮廓特征匹配
            feature_score = self.evaluate_tone_features(
                user_contour, target_tone, user_f0_data['statistics']
            )
            similarity_scores['features'] = feature_score
            
            # 4. 动态时间规整 (DTW) 相似度
            dtw_score = self.calculate_dtw_similarity(user_contour, tone_template)
            similarity_scores['dtw'] = dtw_score
            
            # 综合评分
            if similarity_scores:
                weights = {
                    'correlation_standard': 0.3,
                    'distance_standard': 0.2,
                    'correlation_template': 0.2,
                    'distance_template': 0.1,
                    'features': 0.15,
                    'dtw': 0.05
                }
                
                total_score = 0
                total_weight = 0
                
                for metric, score in similarity_scores.items():
                    weight = weights.get(metric, 0)
                    if weight > 0 and not np.isnan(score):
                        total_score += score * weight
                        total_weight += weight
                
                final_score = (total_score / total_weight) * 100 if total_weight > 0 else 0
            else:
                final_score = 0
            
            result = {
                'score': min(100, max(0, final_score)),
                'target_tone': target_tone,
                'tone_name': self.tone_templates[target_tone]['name'],
                'user_contour': user_contour,
                'standard_contour': standard_contour,
                'template_contour': tone_template,
                'similarity_scores': similarity_scores,
                'user_f0_stats': user_f0_data['statistics'],
                'standard_f0_stats': standard_f0_data['statistics'],
                'feedback': self.generate_tone_feedback(
                    user_contour, tone_template, target_tone, similarity_scores
                )
            }
            
            return result
            
        except Exception as e:
            print(f"声调评测失败: {e}")
            return {'score': 0, 'error': str(e)}
    
    def evaluate_tone_features(self, contour: np.ndarray, 
                              target_tone: int,
                              f0_stats: Dict[str, float]) -> float:
        """
        评估声调特征匹配度
        
        Args:
            contour: 用户的基频轮廓
            target_tone: 目标声调
            f0_stats: 基频统计信息
            
        Returns:
            float: 特征匹配分数 (0-1)
        """
        try:
            if target_tone not in self.tone_templates:
                return 0.0
            
            template_chars = self.tone_templates[target_tone]['characteristics']
            score = 0.0
            
            # 计算轮廓的特征
            if len(contour) >= 3 and not np.any(np.isnan(contour)):
                # 计算斜率特征
                start_slope = contour[1] - contour[0]
                end_slope = contour[-1] - contour[-2]
                overall_slope = contour[-1] - contour[0]
                
                # 计算变异特征
                variation = np.std(contour)
                
                # 根据目标声调评估特征
                if target_tone == 1:  # 第一声 - 高平调
                    if abs(overall_slope) < 0.1:  # 平调
                        score += 0.4
                    if variation < 0.1:  # 变化小
                        score += 0.3
                    if np.mean(contour) > 0.7:  # 高音
                        score += 0.3
                
                elif target_tone == 2:  # 第二声 - 升调
                    if overall_slope > 0.3:  # 上升
                        score += 0.5
                    if end_slope > start_slope:  # 加速上升
                        score += 0.3
                    if 0.3 < np.mean(contour) < 0.8:  # 中等音高
                        score += 0.2
                
                elif target_tone == 3:  # 第三声 - 降升调
                    min_idx = np.argmin(contour)
                    if 1 <= min_idx <= 3:  # V形底部在中间
                        score += 0.4
                    if contour[0] > contour[min_idx] and contour[-1] > contour[min_idx]:  # 两端高于中间
                        score += 0.4
                    if variation > 0.15:  # 变化大
                        score += 0.2
                
                elif target_tone == 4:  # 第四声 - 降调
                    if overall_slope < -0.3:  # 下降
                        score += 0.5
                    if abs(start_slope) < abs(end_slope):  # 加速下降
                        score += 0.3
                    if np.mean(contour) < 0.7:  # 音高下降
                        score += 0.2
                
                elif target_tone == 0:  # 轻声
                    if variation < 0.1:  # 平稳
                        score += 0.5
                    if np.mean(contour) < 0.5:  # 低音
                        score += 0.5
            
            return min(1.0, score)
            
        except Exception as e:
            print(f"声调特征评估失败: {e}")
            return 0.0
    
    def calculate_dtw_similarity(self, contour1: np.ndarray, contour2: np.ndarray) -> float:
        """
        计算动态时间规整相似度
        
        Args:
            contour1: 轮廓1
            contour2: 轮廓2
            
        Returns:
            float: DTW相似度 (0-1)
        """
        try:
            if np.any(np.isnan(contour1)) or np.any(np.isnan(contour2)):
                return 0.0
            
            # 简化的DTW实现
            n, m = len(contour1), len(contour2)
            dtw_matrix = np.full((n + 1, m + 1), np.inf)
            dtw_matrix[0, 0] = 0
            
            for i in range(1, n + 1):
                for j in range(1, m + 1):
                    cost = abs(contour1[i-1] - contour2[j-1])
                    dtw_matrix[i, j] = cost + min(
                        dtw_matrix[i-1, j],      # 插入
                        dtw_matrix[i, j-1],      # 删除
                        dtw_matrix[i-1, j-1]     # 匹配
                    )
            
            # 标准化DTW距离
            dtw_distance = dtw_matrix[n, m] / max(n, m)
            
            # 转换为相似度
            similarity = max(0, 1 - dtw_distance)
            
            return similarity
            
        except Exception as e:
            print(f"DTW相似度计算失败: {e}")
            return 0.0
    
    def generate_tone_feedback(self, user_contour: np.ndarray,
                              template_contour: np.ndarray,
                              target_tone: int,
                              similarity_scores: Dict[str, float]) -> str:
        """
        生成声调评测反馈
        
        Args:
            user_contour: 用户轮廓
            template_contour: 模板轮廓
            target_tone: 目标声调
            similarity_scores: 相似度分数
            
        Returns:
            str: 反馈文本
        """
        try:
            feedback = []
            
            if target_tone not in self.tone_templates:
                return "无法生成反馈：未知声调。"
            
            tone_info = self.tone_templates[target_tone]
            avg_score = np.mean([s for s in similarity_scores.values() if not np.isnan(s)])
            
            # 总体评价
            if avg_score > 0.8:
                feedback.append(f"{tone_info['name']}发音准确！")
            elif avg_score > 0.6:
                feedback.append(f"{tone_info['name']}发音基本正确，可以进一步改进。")
            else:
                feedback.append(f"{tone_info['name']}发音需要改进。")
            
            # 添加声调描述
            feedback.append(f"标准发音：{tone_info['description']}")
            
            # 具体建议
            if not np.any(np.isnan(user_contour)) and not np.any(np.isnan(template_contour)):
                user_slope = user_contour[-1] - user_contour[0]
                template_slope = template_contour[-1] - template_contour[0]
                
                if target_tone == 1:  # 第一声
                    if abs(user_slope) > 0.2:
                        feedback.append("第一声应该保持平调，避免音高起伏。")
                    if np.mean(user_contour) < 0.6:
                        feedback.append("第一声的音高应该保持在较高水平。")
                
                elif target_tone == 2:  # 第二声
                    if user_slope < 0:
                        feedback.append("第二声应该上升，不是下降。")
                    elif user_slope < 0.3:
                        feedback.append("第二声的上升幅度需要更大一些。")
                
                elif target_tone == 3:  # 第三声
                    min_pos = np.argmin(user_contour)
                    if min_pos == 0:
                        feedback.append("第三声应该先下降再上升，不是一直上升。")
                    elif min_pos == len(user_contour) - 1:
                        feedback.append("第三声应该有上升的部分，不是一直下降。")
                    else:
                        variation = np.std(user_contour)
                        if variation < 0.1:
                            feedback.append("第三声的音高变化应该更明显。")
                
                elif target_tone == 4:  # 第四声
                    if user_slope > 0:
                        feedback.append("第四声应该下降，不是上升。")
                    elif user_slope > -0.3:
                        feedback.append("第四声的下降幅度需要更大一些。")
                
                elif target_tone == 0:  # 轻声
                    if np.std(user_contour) > 0.15:
                        feedback.append("轻声应该保持平稳，避免音高变化。")
                    if np.mean(user_contour) > 0.6:
                        feedback.append("轻声的音高应该比较低。")
            
            # 根据相似度分数给出具体建议
            if similarity_scores.get('correlation_template', 0) < 0.5:
                feedback.append("音调轮廓与标准模式差异较大，需要重点练习。")
            
            return " ".join(feedback)
            
        except Exception as e:
            print(f"生成声调反馈失败: {e}")
            return "无法生成详细反馈。"
    
    def analyze_tone_pattern(self, f0_contour: np.ndarray) -> Dict[str, Any]:
        """
        分析基频轮廓的声调模式
        
        Args:
            f0_contour: 基频轮廓
            
        Returns:
            Dict[str, Any]: 声调模式分析结果
        """
        try:
            if np.any(np.isnan(f0_contour)) or len(f0_contour) < 3:
                return {'pattern': 'unknown', 'confidence': 0.0}
            
            # 计算轮廓特征
            overall_slope = f0_contour[-1] - f0_contour[0]
            variation = np.std(f0_contour)
            mean_level = np.mean(f0_contour)
            
            # 寻找转折点
            diff = np.diff(f0_contour)
            sign_changes = np.sum(np.diff(np.sign(diff)) != 0)
            
            # 模式识别
            confidences = {}
            
            # 第一声：平调
            if abs(overall_slope) < 0.1 and variation < 0.1 and mean_level > 0.6:
                confidences[1] = 0.9
            elif abs(overall_slope) < 0.2 and variation < 0.15:
                confidences[1] = 0.6
            
            # 第二声：升调
            if overall_slope > 0.4 and variation > 0.1:
                confidences[2] = 0.9
            elif overall_slope > 0.2:
                confidences[2] = 0.6
            
            # 第三声：降升调
            if sign_changes >= 1 and variation > 0.15:
                min_idx = np.argmin(f0_contour)
                if 1 <= min_idx <= len(f0_contour) - 2:
                    confidences[3] = 0.8
            
            # 第四声：降调
            if overall_slope < -0.3 and variation > 0.1:
                confidences[4] = 0.9
            elif overall_slope < -0.1:
                confidences[4] = 0.6
            
            # 轻声：低平调
            if variation < 0.1 and mean_level < 0.5:
                confidences[0] = 0.8
            
            # 确定最可能的声调
            if confidences:
                best_tone = max(confidences.keys(), key=lambda x: confidences[x])
                best_confidence = confidences[best_tone]
            else:
                best_tone = 'unknown'
                best_confidence = 0.0
            
            return {
                'pattern': best_tone,
                'confidence': best_confidence,
                'all_confidences': confidences,
                'features': {
                    'overall_slope': overall_slope,
                    'variation': variation,
                    'mean_level': mean_level,
                    'sign_changes': sign_changes
                }
            }
            
        except Exception as e:
            print(f"声调模式分析失败: {e}")
            return {'pattern': 'unknown', 'confidence': 0.0}

# 测试代码
if __name__ == "__main__":
    evaluator = ToneEvaluator()
    
    # 创建测试音频
    import librosa
    test_audio_path = "/mnt/e/Code/Outsourcing/普通话测评/mandarin_evaluation/data/音频/01.MP3"
    
    try:
        audio_data, sr = librosa.load(test_audio_path, sr=16000)
        print(f"加载测试音频: {len(audio_data)/sr:.2f}秒")
        
        # 测试基频提取
        f0_data = evaluator.extract_f0_contour(audio_data[:int(0.8*sr)])
        if 'error' not in f0_data:
            print(f"基频统计: {f0_data['statistics']}")
            
            # 测试声调评测
            tone_result = evaluator.evaluate_tone(
                audio_data[:int(0.8*sr)],
                audio_data[:int(0.8*sr)],
                1,  # 测试第一声
                0.8
            )
            print(f"声调评测结果: {tone_result['score']:.1f}%")
            print(f"反馈: {tone_result['feedback']}")
        else:
            print(f"基频提取失败: {f0_data['error']}")
        
    except Exception as e:
        print(f"测试失败: {e}")