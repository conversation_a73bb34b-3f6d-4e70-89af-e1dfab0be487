#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主评测引擎
集成所有评测模块，提供统一的评测接口
"""

import numpy as np
import librosa
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import json
import sys
import threading
import time

# 导入各个评测模块
from .audio_processor import AudioProcessor
from .text_processor import TextProcessor
from .phoneme_evaluator import PhonemeEvaluator
from .tone_evaluator import ToneEvaluator
from .fluency_evaluator import FluencyEvaluator

class MandarinEvaluator:
    """普通话评测主引擎"""
    
    def __init__(self, sample_rate: int = 16000):
        """
        初始化评测引擎
        
        Args:
            sample_rate: 采样率
        """
        self.sample_rate = sample_rate
        
        # 初始化各个评测器
        self.audio_processor = AudioProcessor(sample_rate)
        self.text_processor = TextProcessor()
        self.phoneme_evaluator = PhonemeEvaluator(sample_rate)
        self.tone_evaluator = ToneEvaluator(sample_rate)
        self.fluency_evaluator = FluencyEvaluator(sample_rate)
        
        # 评测权重配置
        self.evaluation_weights = {
            'consonant': 0.25,   # 声母权重
            'vowel': 0.25,       # 韵母权重
            'tone': 0.25,        # 声调权重
            'fluency': 0.25      # 流畅性权重
        }
        
        # 加载标准音频路径映射
        self.standard_audio_path = None
        self.texts_data = {}
        
    def set_data_path(self, data_path: str):
        """
        设置数据路径

        Args:
            data_path: 数据目录路径
        """
        try:
            data_dir = Path(data_path)

            # 设置标准音频目录 - 先检查音频子目录，再检查data目录本身
            audio_dir = data_dir / "音频"
            if audio_dir.exists():
                self.standard_audio_path = audio_dir
                print(f"使用音频目录: {audio_dir}")
            elif data_dir.exists():
                # 如果音频文件直接在data目录下
                self.standard_audio_path = data_dir
                print(f"使用数据目录: {data_dir}")
            else:
                print(f"数据目录不存在: {data_dir}")

            # 加载文本数据
            texts_file = data_dir / "parsed_texts.json"
            if texts_file.exists():
                with open(texts_file, 'r', encoding='utf-8') as f:
                    self.texts_data = json.load(f)
                print(f"加载了 {len(self.texts_data)} 个标准文本")
            else:
                print(f"文本数据文件不存在: {texts_file}")

        except Exception as e:
            print(f"设置数据路径失败: {e}")
    
    def load_standard_audio(self, text_id: str) -> Optional[Tuple[np.ndarray, int]]:
        """
        加载标准音频
        
        Args:
            text_id: 文本ID
            
        Returns:
            Optional[Tuple[np.ndarray, int]]: (音频数据, 采样率) 或 None
        """
        try:
            if not self.standard_audio_path:
                return None
            
            # 尝试不同的音频文件格式
            audio_file = self.standard_audio_path / f"{text_id.zfill(2)}.MP3"
            if not audio_file.exists():
                audio_file = self.standard_audio_path / f"{text_id}.mp3"
            if not audio_file.exists():
                audio_file = self.standard_audio_path / f"{text_id}.wav"
            
            if audio_file.exists():
                return self.audio_processor.load_audio(str(audio_file))
            else:
                print(f"找不到标准音频文件: {text_id}")
                return None
                
        except Exception as e:
            print(f"加载标准音频失败: {e}")
            return None
    
    def segment_audio_by_text(self, audio_data: np.ndarray, 
                             text: str) -> List[Dict[str, Any]]:
        """
        根据文本将音频分段
        
        Args:
            audio_data: 音频数据
            text: 对应文本
            
        Returns:
            List[Dict[str, Any]]: 音频段信息列表
        """
        try:
            # 处理文本，获取拼音信息
            text_analysis = self.text_processor.process_text(text)
            if not text_analysis:
                return []
            
            characters = text_analysis['characters']
            syllables = text_analysis['syllables']
            
            if not characters or not syllables:
                return []
            
            # 检测语音段
            segments_data = self.fluency_evaluator.detect_speech_segments(audio_data)
            if not segments_data or not segments_data['speech_segments']:
                return []
            
            speech_segments = segments_data['speech_segments']
            total_speech_time = sum(seg['duration'] for seg in speech_segments)
            
            # 按字符数分配时间
            char_segments = []
            time_per_char = total_speech_time / len(characters)
            current_time = 0
            
            for i, (char, syllable) in enumerate(zip(characters, syllables)):
                # 分析拼音
                syllable_analysis = self.text_processor.analyze_pinyin(syllable)
                
                # 计算该音节的时长（可以根据复杂度调整）
                base_duration = time_per_char
                if syllable_analysis['tone'] == 3:  # 第三声需要更长时间
                    base_duration *= 1.2
                elif len(syllable_analysis['final']) > 2:  # 复合韵母
                    base_duration *= 1.1
                
                char_segments.append({
                    'character': char,
                    'syllable': syllable,
                    'syllable_analysis': syllable_analysis,
                    'start_time': current_time,
                    'end_time': current_time + base_duration,
                    'duration': base_duration
                })
                
                current_time += base_duration
            
            return char_segments
            
        except Exception as e:
            print(f"音频分段失败: {e}")
            return []
    
    def evaluate_single_syllable(self, user_audio: np.ndarray,
                                standard_audio: np.ndarray,
                                syllable_info: Dict[str, Any],
                                progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        评测单个音节
        
        Args:
            user_audio: 用户音频段
            standard_audio: 标准音频段
            syllable_info: 音节信息
            progress_callback: 进度回调函数
            
        Returns:
            Dict[str, Any]: 音节评测结果
        """
        try:
            syllable_analysis = syllable_info['syllable_analysis']
            character = syllable_info['character']
            syllable = syllable_info['syllable']
            duration = syllable_info['duration']
            
            results = {
                'character': character,
                'syllable': syllable,
                'syllable_analysis': syllable_analysis
            }
            
            # 1. 声母评测
            if progress_callback:
                progress_callback(f"评测声母: {character}")
            
            if syllable_analysis['initial']:
                consonant_result = self.phoneme_evaluator.evaluate_initial(
                    user_audio, standard_audio, 
                    syllable_analysis['initial'], duration
                )
                results['consonant'] = consonant_result
            else:
                # 零声母
                results['consonant'] = {
                    'score': 90,  # 零声母给默认高分
                    'feedback': '零声母发音正确'
                }
            
            # 2. 韵母评测
            if progress_callback:
                progress_callback(f"评测韵母: {character}")
                
            vowel_result = self.phoneme_evaluator.evaluate_final(
                user_audio, standard_audio,
                syllable_analysis['final'], duration
            )
            results['vowel'] = vowel_result
            
            # 3. 声调评测
            if progress_callback:
                progress_callback(f"评测声调: {character}")
                
            tone_result = self.tone_evaluator.evaluate_tone(
                user_audio, standard_audio,
                syllable_analysis['tone'], duration
            )
            results['tone'] = tone_result
            
            # 4. 计算音节总分
            consonant_score = results['consonant'].get('score', 0)
            vowel_score = results['vowel'].get('score', 0)
            tone_score = results['tone'].get('score', 0)
            
            # 音节内权重
            syllable_weights = {'consonant': 0.35, 'vowel': 0.35, 'tone': 0.3}
            syllable_score = (
                consonant_score * syllable_weights['consonant'] +
                vowel_score * syllable_weights['vowel'] +
                tone_score * syllable_weights['tone']
            )
            
            results['syllable_score'] = syllable_score
            
            return results
            
        except Exception as e:
            print(f"音节评测失败: {e}")
            return {
                'character': syllable_info.get('character', ''),
                'syllable': syllable_info.get('syllable', ''),
                'error': str(e)
            }
    
    def evaluate_pronunciation(self, user_audio: np.ndarray,
                             text_id: str,
                             progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        评测发音准确性
        
        Args:
            user_audio: 用户录音
            text_id: 文本ID
            progress_callback: 进度回调函数
            
        Returns:
            Dict[str, Any]: 发音评测结果
        """
        try:
            # 获取文本内容
            if text_id not in self.texts_data:
                return {'error': f'未找到文本ID: {text_id}'}
            
            text = self.texts_data[text_id]
            
            if progress_callback:
                progress_callback("加载标准音频...")
            
            # 加载标准音频
            standard_result = self.load_standard_audio(text_id)
            if not standard_result:
                return {'error': f'无法加载标准音频: {text_id}'}
            
            standard_audio, _ = standard_result
            
            if progress_callback:
                progress_callback("分析文本和音频...")
            
            # 音频分段
            user_segments = self.segment_audio_by_text(user_audio, text)
            standard_segments = self.segment_audio_by_text(standard_audio, text)
            
            if not user_segments or not standard_segments:
                return {'error': '音频分段失败'}
            
            # 确保段数一致
            min_segments = min(len(user_segments), len(standard_segments))
            user_segments = user_segments[:min_segments]
            standard_segments = standard_segments[:min_segments]
            
            if progress_callback:
                progress_callback(f"开始逐音节评测，共 {min_segments} 个音节...")
            
            # 逐音节评测
            syllable_results = []
            consonant_scores = []
            vowel_scores = []
            tone_scores = []
            
            for i, (user_seg, standard_seg) in enumerate(zip(user_segments, standard_segments)):
                if progress_callback:
                    progress = 20 + (i / min_segments) * 60  # 20%-80%的进度用于音节评测
                    progress_callback(f"评测音节 {i+1}/{min_segments}: {user_seg['character']}")
                
                # 提取音频段
                user_start = int(user_seg['start_time'] * self.sample_rate)
                user_end = int(user_seg['end_time'] * self.sample_rate)
                user_audio_seg = user_audio[user_start:user_end]
                
                standard_start = int(standard_seg['start_time'] * self.sample_rate)
                standard_end = int(standard_seg['end_time'] * self.sample_rate)
                standard_audio_seg = standard_audio[standard_start:standard_end]
                
                # 评测音节
                syllable_result = self.evaluate_single_syllable(
                    user_audio_seg, standard_audio_seg, user_seg, progress_callback
                )
                
                syllable_results.append(syllable_result)
                
                # 收集分数
                if 'consonant' in syllable_result:
                    consonant_scores.append(syllable_result['consonant'].get('score', 0))
                if 'vowel' in syllable_result:
                    vowel_scores.append(syllable_result['vowel'].get('score', 0))
                if 'tone' in syllable_result:
                    tone_scores.append(syllable_result['tone'].get('score', 0))
            
            # 计算各维度平均分
            avg_consonant = np.mean(consonant_scores) if consonant_scores else 0
            avg_vowel = np.mean(vowel_scores) if vowel_scores else 0
            avg_tone = np.mean(tone_scores) if tone_scores else 0
            
            return {
                'text_id': text_id,
                'text': text,
                'syllable_results': syllable_results,
                'dimension_scores': {
                    'consonant': avg_consonant,
                    'vowel': avg_vowel,
                    'tone': avg_tone
                },
                'statistics': {
                    'total_syllables': len(syllable_results),
                    'consonant_scores': consonant_scores,
                    'vowel_scores': vowel_scores,
                    'tone_scores': tone_scores
                }
            }
            
        except Exception as e:
            print(f"发音评测失败: {e}")
            return {'error': str(e)}
    
    def evaluate_complete(self, user_audio: np.ndarray,
                         text_id: str,
                         progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        完整评测（包括发音和流畅性）
        
        Args:
            user_audio: 用户录音
            text_id: 文本ID
            progress_callback: 进度回调函数
            
        Returns:
            Dict[str, Any]: 完整评测结果
        """
        try:
            if progress_callback:
                progress_callback("开始完整评测...")
            
            # 获取文本内容
            if text_id not in self.texts_data:
                return {'error': f'未找到文本ID: {text_id}'}
            
            text = self.texts_data[text_id]
            
            # 1. 发音准确性评测
            if progress_callback:
                progress_callback("评测发音准确性...")
            
            pronunciation_result = self.evaluate_pronunciation(
                user_audio, text_id, 
                lambda msg: progress_callback(f"发音评测: {msg}") if progress_callback else None
            )
            
            if 'error' in pronunciation_result:
                return pronunciation_result
            
            # 2. 流畅性评测
            if progress_callback:
                progress_callback("评测流畅性...")
            
            # 加载标准音频用于流畅性对比
            standard_result = self.load_standard_audio(text_id)
            if standard_result:
                standard_audio, _ = standard_result
                
                # 计算音节数
                text_analysis = self.text_processor.process_text(text)
                syllable_count = text_analysis.get('character_count', 0) if text_analysis else 0
                
                fluency_result = self.fluency_evaluator.evaluate_fluency(
                    user_audio, standard_audio, text, syllable_count
                )
            else:
                fluency_result = {'score': 70, 'error': '无标准音频对比'}
            
            # 3. 综合评分
            if progress_callback:
                progress_callback("计算综合评分...")
            
            dimension_scores = pronunciation_result['dimension_scores']
            consonant_score = dimension_scores['consonant']
            vowel_score = dimension_scores['vowel']
            tone_score = dimension_scores['tone']
            fluency_score = fluency_result.get('score', 0)
            
            # 使用配置的权重计算总分
            total_score = (
                consonant_score * self.evaluation_weights['consonant'] +
                vowel_score * self.evaluation_weights['vowel'] +
                tone_score * self.evaluation_weights['tone'] +
                fluency_score * self.evaluation_weights['fluency']
            )
            
            # 4. 生成综合反馈
            feedback = self.generate_comprehensive_feedback(
                consonant_score, vowel_score, tone_score, fluency_score, total_score
            )
            
            # 5. 计算等级
            grade = self.calculate_grade(total_score)
            
            result = {
                'text_id': text_id,
                'text': text,
                'total_score': total_score,
                'grade': grade,
                'dimension_scores': {
                    'consonant': consonant_score,
                    'vowel': vowel_score,
                    'tone': tone_score,
                    'fluency': fluency_score
                },
                'detailed_results': {
                    'pronunciation': pronunciation_result,
                    'fluency': fluency_result
                },
                'feedback': feedback,
                'evaluation_time': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            if progress_callback:
                progress_callback("评测完成！")
            
            return result
            
        except Exception as e:
            print(f"完整评测失败: {e}")
            return {'error': str(e)}
    
    def generate_comprehensive_feedback(self, consonant_score: float, 
                                       vowel_score: float,
                                       tone_score: float, 
                                       fluency_score: float,
                                       total_score: float) -> str:
        """
        生成综合反馈
        
        Args:
            consonant_score: 声母得分
            vowel_score: 韵母得分
            tone_score: 声调得分
            fluency_score: 流畅性得分
            total_score: 总分
            
        Returns:
            str: 综合反馈文本
        """
        try:
            feedback = []
            
            # 总体评价
            if total_score >= 90:
                feedback.append("🎉 发音非常标准，普通话水平优秀！")
            elif total_score >= 80:
                feedback.append("👍 发音比较标准，普通话水平良好！")
            elif total_score >= 70:
                feedback.append("📈 发音基本标准，还有提升空间。")
            elif total_score >= 60:
                feedback.append("📚 发音需要改进，建议加强练习。")
            else:
                feedback.append("💪 发音需要大幅改进，建议系统学习。")
            
            # 各维度分析
            scores = {
                '声母': consonant_score,
                '韵母': vowel_score, 
                '声调': tone_score,
                '流畅性': fluency_score
            }
            
            # 找出最强和最弱的方面
            best_aspect = max(scores.keys(), key=lambda x: scores[x])
            worst_aspect = min(scores.keys(), key=lambda x: scores[x])
            
            if scores[best_aspect] >= 85:
                feedback.append(f"✨ {best_aspect}表现优秀！")
            
            if scores[worst_aspect] < 70:
                feedback.append(f"🎯 {worst_aspect}需要重点改进。")
            
            # 具体建议
            suggestions = []
            
            if consonant_score < 75:
                suggestions.append("加强声母发音练习，注意区分平翘舌音")
            
            if vowel_score < 75:
                suggestions.append("练习韵母发音，注意口形和舌位")
            
            if tone_score < 75:
                suggestions.append("加强声调练习，注意四声的区别和轻声")
            
            if fluency_score < 75:
                suggestions.append("提高语言流畅性，注意语速和停顿")
            
            if suggestions:
                feedback.append("\n📝 改进建议：")
                for i, suggestion in enumerate(suggestions, 1):
                    feedback.append(f"  {i}. {suggestion}")
            
            return "\n".join(feedback)
            
        except Exception as e:
            print(f"生成综合反馈失败: {e}")
            return "评测完成，请查看详细结果。"
    
    def calculate_grade(self, score: float) -> str:
        """
        根据分数计算等级
        
        Args:
            score: 总分
            
        Returns:
            str: 等级字符串
        """
        if score >= 97:
            return "一级甲等"
        elif score >= 92:
            return "一级乙等"
        elif score >= 87:
            return "二级甲等"
        elif score >= 80:
            return "二级乙等"
        elif score >= 70:
            return "三级甲等"
        elif score >= 60:
            return "三级乙等"
        else:
            return "不合格"
    
    def generate_error_report(self, result: Dict[str, Any]) -> str:
        """
        生成详细错误标注报告

        Args:
            result: 完整评测结果

        Returns:
            str: 详细错误报告文本
        """
        try:
            if 'error' in result:
                return f"评测失败: {result['error']}"

            # 基本信息
            text = result.get('text', '')
            total_score = result.get('total_score', 0)
            grade = result.get('grade', '未知')

            # 获取音节评测结果
            pronunciation_result = result.get('detailed_results', {}).get('pronunciation', {})
            syllable_results = pronunciation_result.get('syllable_results', [])

            if not syllable_results:
                return "无法生成错误报告：缺少音节评测数据"

            # 开始构建报告
            report_lines = []
            report_lines.append("=" * 50)
            report_lines.append("普通话评测详细错误报告")
            report_lines.append("=" * 50)
            report_lines.append(f"文本：{text[:50]}{'...' if len(text) > 50 else ''}")
            report_lines.append(f"总分：{total_score:.1f}分 ({grade})")
            report_lines.append("")

            # 分析错误
            errors = []
            correct_count = 0
            total_chars = len(syllable_results)

            consonant_errors = 0
            vowel_errors = 0
            tone_errors = 0

            for i, syllable_result in enumerate(syllable_results):
                char = syllable_result.get('character', '')
                syllable = syllable_result.get('syllable', '')
                syllable_analysis = syllable_result.get('syllable_analysis', {})

                # 获取各维度分数
                consonant_score = syllable_result.get('consonant', {}).get('score', 0)
                vowel_score = syllable_result.get('vowel', {}).get('score', 0)
                tone_score = syllable_result.get('tone', {}).get('score', 0)
                syllable_score = syllable_result.get('syllable_score', 0)

                # 判断是否有错误（阈值设为75分）
                error_threshold = 75
                char_errors = []

                if consonant_score < error_threshold:
                    consonant_errors += 1
                    initial = syllable_analysis.get('initial', '')
                    if initial:
                        char_errors.append({
                            'type': '声母',
                            'score': consonant_score,
                            'standard': initial,
                            'feedback': self._get_consonant_error_feedback(initial, consonant_score)
                        })

                if vowel_score < error_threshold:
                    vowel_errors += 1
                    final = syllable_analysis.get('final', '')
                    char_errors.append({
                        'type': '韵母',
                        'score': vowel_score,
                        'standard': final,
                        'feedback': self._get_vowel_error_feedback(final, vowel_score)
                    })

                if tone_score < error_threshold:
                    tone_errors += 1
                    tone = syllable_analysis.get('tone', 0)
                    tone_name = self._get_tone_name(tone)
                    char_errors.append({
                        'type': '声调',
                        'score': tone_score,
                        'standard': tone_name,
                        'feedback': self._get_tone_error_feedback(tone, tone_score)
                    })

                if char_errors:
                    errors.append({
                        'position': i + 1,
                        'character': char,
                        'syllable': syllable,
                        'errors': char_errors,
                        'syllable_score': syllable_score
                    })
                else:
                    correct_count += 1

            # 错误详情部分
            if errors:
                report_lines.append("错误详情：")
                report_lines.append("-" * 30)

                for error_info in errors:
                    pos = error_info['position']
                    char = error_info['character']
                    syllable = error_info['syllable']
                    char_errors = error_info['errors']
                    score = error_info['syllable_score']

                    report_lines.append(f"第{pos}个字：{char} [{syllable}] (得分: {score:.1f})")

                    for error in char_errors:
                        error_type = error['type']
                        error_score = error['score']
                        standard = error['standard']
                        feedback = error['feedback']

                        severity = "❌" if error_score < 60 else "⚠️"
                        report_lines.append(f"  {severity} {error_type}错误 ({error_score:.1f}分)")
                        report_lines.append(f"    标准读音：{standard}")
                        report_lines.append(f"    建议：{feedback}")

                    report_lines.append("")
            else:
                report_lines.append("🎉 恭喜！未发现明显发音错误。")
                report_lines.append("")

            # 发音统计部分
            report_lines.append("发音统计：")
            report_lines.append("-" * 30)
            report_lines.append(f"总字数：{total_chars}字")
            report_lines.append(f"正确发音：{correct_count}字 ({correct_count/total_chars*100:.1f}%)")
            report_lines.append(f"需要改进：{len(errors)}字 ({len(errors)/total_chars*100:.1f}%)")
            report_lines.append(f"声母错误：{consonant_errors}个")
            report_lines.append(f"韵母错误：{vowel_errors}个")
            report_lines.append(f"声调错误：{tone_errors}个")
            report_lines.append("")

            # 改进建议部分
            suggestions = self._generate_improvement_suggestions(
                consonant_errors, vowel_errors, tone_errors, total_chars
            )
            if suggestions:
                report_lines.append("改进建议：")
                report_lines.append("-" * 30)
                for i, suggestion in enumerate(suggestions, 1):
                    report_lines.append(f"{i}. {suggestion}")
                report_lines.append("")

            # 评测时间
            eval_time = result.get('evaluation_time', '未知')
            report_lines.append(f"评测时间：{eval_time}")
            report_lines.append("=" * 50)

            return "\n".join(report_lines)

        except Exception as e:
            print(f"生成错误报告失败: {e}")
            return f"生成错误报告失败: {str(e)}"

    def _get_consonant_error_feedback(self, initial: str, score: float) -> str:
        """生成声母错误反馈"""
        if initial in ['zh', 'ch', 'sh']:
            return "注意翘舌音的舌位，舌尖要卷起"
        elif initial in ['z', 'c', 's']:
            return "注意平舌音与翘舌音的区别"
        elif initial in ['j', 'q', 'x']:
            return "注意舌面音的发音位置"
        elif initial in ['b', 'p']:
            return "注意双唇音的送气区别"
        elif initial in ['d', 't']:
            return "注意舌尖音的送气区别"
        elif initial in ['g', 'k', 'h']:
            return "注意舌根音的发音部位"
        else:
            return "发音力度和清晰度需要加强"

    def _get_vowel_error_feedback(self, final: str, score: float) -> str:
        """生成韵母错误反馈"""
        if final in ['ai', 'ei', 'ao', 'ou']:
            return "注意复韵母的动程变化，口形要到位"
        elif final in ['an', 'en', 'in', 'un']:
            return "注意前鼻音的舌位，舌尖要抵住上齿龈"
        elif final in ['ang', 'eng', 'ing', 'ong']:
            return "注意后鼻音的发音部位，舌根要抵住软腭"
        elif final in ['v', 've']:
            return "注意撮口音的唇形，嘴唇要圆"
        elif final in ['i', 'u', 'v']:
            return "注意单韵母的口形和舌位"
        else:
            return "口形和舌位需要调整，发音要饱满"

    def _get_tone_error_feedback(self, tone: int, score: float) -> str:
        """生成声调错误反馈"""
        tone_feedback = {
            1: "第一声要保持高平调，音高要稳定",
            2: "第二声要从中音升到高音，有明显的上升趋势",
            3: "第三声要先降后升，在低音处有明显的转折",
            4: "第四声要从高音快速降到低音，降调要明显",
            0: "轻声要读得又轻又短，不要过重"
        }
        return tone_feedback.get(tone, "声调发音需要加强练习")

    def _get_tone_name(self, tone: int) -> str:
        """获取声调名称"""
        tone_names = {1: "第一声", 2: "第二声", 3: "第三声", 4: "第四声", 0: "轻声"}
        return tone_names.get(tone, "未知声调")

    def _generate_improvement_suggestions(self, consonant_errors: int, vowel_errors: int,
                                        tone_errors: int, total_chars: int) -> list:
        """生成改进建议"""
        suggestions = []

        # 根据错误比例生成建议
        consonant_ratio = consonant_errors / total_chars
        vowel_ratio = vowel_errors / total_chars
        tone_ratio = tone_errors / total_chars

        if consonant_ratio > 0.2:
            suggestions.append("重点练习声母发音，特别是平翘舌音的区别")
        elif consonant_ratio > 0.1:
            suggestions.append("加强声母发音练习，注意发音部位")

        if vowel_ratio > 0.2:
            suggestions.append("重点练习韵母发音，注意口形变化和舌位")
        elif vowel_ratio > 0.1:
            suggestions.append("加强韵母发音练习，特别是复韵母和鼻韵母")

        if tone_ratio > 0.2:
            suggestions.append("重点练习声调，注意四声的调值变化")
        elif tone_ratio > 0.1:
            suggestions.append("加强声调练习，注意声调的起伏变化")

        # 通用建议
        if consonant_errors + vowel_errors + tone_errors > total_chars * 0.3:
            suggestions.append("建议跟读标准音频，模仿标准发音")
            suggestions.append("可以录音对比，找出自己的发音问题")

        if not suggestions:
            suggestions.append("继续保持，可以挑战更难的文本")

        return suggestions

    def get_evaluation_summary(self, result: Dict[str, Any]) -> str:
        """
        获取评测摘要

        Args:
            result: 评测结果

        Returns:
            str: 评测摘要文本
        """
        try:
            if 'error' in result:
                return f"评测失败: {result['error']}"

            total_score = result.get('total_score', 0)
            grade = result.get('grade', '未知')

            dimension_scores = result.get('dimension_scores', {})
            consonant = dimension_scores.get('consonant', 0)
            vowel = dimension_scores.get('vowel', 0)
            tone = dimension_scores.get('tone', 0)
            fluency = dimension_scores.get('fluency', 0)

            summary = f"""
=== 普通话评测报告 ===

总分: {total_score:.1f}分
等级: {grade}

各维度得分:
• 声母: {consonant:.1f}分
• 韵母: {vowel:.1f}分
• 声调: {tone:.1f}分
• 流畅性: {fluency:.1f}分

评测时间: {result.get('evaluation_time', '未知')}
"""
            return summary

        except Exception as e:
            print(f"生成评测摘要失败: {e}")
            return "无法生成评测摘要"

# 测试代码
if __name__ == "__main__":
    # 创建评测引擎
    evaluator = MandarinEvaluator()
    
    # 设置数据路径
    data_path = "/mnt/e/Code/Outsourcing/普通话测评/mandarin_evaluation/data"
    evaluator.set_data_path(data_path)
    
    # 测试加载标准音频
    result = evaluator.load_standard_audio("1")
    if result:
        audio_data, sr = result
        print(f"成功加载标准音频，长度: {len(audio_data)/sr:.2f}秒")
        
        # 测试完整评测
        def progress_callback(message):
            print(f"进度: {message}")
        
        evaluation_result = evaluator.evaluate_complete(
            audio_data, "1", progress_callback
        )
        
        if 'error' not in evaluation_result:
            print(f"评测完成，总分: {evaluation_result['total_score']:.1f}")
            print(f"等级: {evaluation_result['grade']}")
            print("\n" + evaluation_result['feedback'])
        else:
            print(f"评测失败: {evaluation_result['error']}")
    else:
        print("无法加载测试音频")