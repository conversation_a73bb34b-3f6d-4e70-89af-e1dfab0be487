#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流畅性评测模块
评测语音的流畅性，包括语速、停顿、节奏等方面
"""

import numpy as np
import librosa
from scipy import signal
from scipy.stats import variation
from typing import Dict, List, Tuple, Any, Optional
import re
import warnings
warnings.filterwarnings('ignore')

class FluencyEvaluator:
    """流畅性评测器"""
    
    def __init__(self, sample_rate: int = 16000):
        """
        初始化流畅性评测器
        
        Args:
            sample_rate: 采样率
        """
        self.sample_rate = sample_rate
        self.frame_length = int(0.025 * sample_rate)  # 25ms窗口
        self.hop_length = int(0.01 * sample_rate)     # 10ms步长
        
        # 标准参数
        self.standard_speech_rate = 5.0  # 标准语速：每秒5个音节
        self.standard_pause_ratio = 0.15  # 标准停顿比例：15%
        
        # 初始化评测标准
        self.initialize_fluency_standards()
    
    def initialize_fluency_standards(self):
        """初始化流畅性评测标准"""
        # 语速标准（音节/秒）
        self.speech_rate_standards = {
            'very_slow': (0, 3.0),
            'slow': (3.0, 4.0),
            'normal': (4.0, 6.0),
            'fast': (6.0, 7.5),
            'very_fast': (7.5, float('inf'))
        }
        
        # 停顿标准
        self.pause_standards = {
            'short_pause': (0.2, 0.5),    # 短停顿
            'medium_pause': (0.5, 1.0),   # 中等停顿
            'long_pause': (1.0, 2.0),     # 长停顿
            'very_long_pause': (2.0, float('inf'))  # 超长停顿
        }
        
        # 节奏变异度标准
        self.rhythm_standards = {
            'very_stable': (0, 0.2),
            'stable': (0.2, 0.4),
            'normal': (0.4, 0.8),
            'unstable': (0.8, 1.2),
            'very_unstable': (1.2, float('inf'))
        }
    
    def extract_energy_contour(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        提取音频的能量轮廓
        
        Args:
            audio_data: 音频数据
            
        Returns:
            Dict[str, Any]: 能量轮廓信息
        """
        try:
            # 计算短时能量
            energy = librosa.feature.rms(
                y=audio_data, 
                frame_length=self.frame_length,
                hop_length=self.hop_length
            )[0]
            
            # 时间轴
            times = librosa.frames_to_time(
                np.arange(len(energy)),
                sr=self.sample_rate,
                hop_length=self.hop_length
            )
            
            # 平滑能量轮廓
            smoothed_energy = signal.savgol_filter(energy, 5, 2)
            
            return {
                'energy': energy,
                'smoothed_energy': smoothed_energy,
                'times': times,
                'statistics': {
                    'mean_energy': np.mean(energy),
                    'std_energy': np.std(energy),
                    'max_energy': np.max(energy),
                    'min_energy': np.min(energy),
                    'energy_range': np.max(energy) - np.min(energy)
                }
            }
            
        except Exception as e:
            print(f"能量轮廓提取失败: {e}")
            return {}
    
    def detect_speech_segments(self, audio_data: np.ndarray, 
                              energy_threshold: float = 0.01,
                              min_speech_duration: float = 0.1,
                              min_pause_duration: float = 0.1) -> Dict[str, Any]:
        """
        检测语音段和静音段
        
        Args:
            audio_data: 音频数据
            energy_threshold: 能量阈值
            min_speech_duration: 最小语音段长度（秒）
            min_pause_duration: 最小停顿长度（秒）
            
        Returns:
            Dict[str, Any]: 语音段检测结果
        """
        try:
            # 提取能量轮廓
            energy_data = self.extract_energy_contour(audio_data)
            if not energy_data:
                return {}
            
            energy = energy_data['smoothed_energy']
            times = energy_data['times']
            
            # 自适应阈值：如果固定阈值太高，使用能量均值的一定比例
            adaptive_threshold = max(energy_threshold, np.mean(energy) * 0.3)
            
            # 检测语音/静音
            speech_mask = energy > adaptive_threshold
            
            # 找到语音段的边界
            speech_segments = []
            pause_segments = []
            
            # 状态变化点
            diff = np.diff(speech_mask.astype(int))
            speech_starts = np.where(diff == 1)[0] + 1
            speech_ends = np.where(diff == -1)[0] + 1
            
            # 处理边界情况
            if speech_mask[0]:
                speech_starts = np.concatenate([[0], speech_starts])
            if speech_mask[-1]:
                speech_ends = np.concatenate([speech_ends, [len(speech_mask)]])
            
            # 确保配对
            min_len = min(len(speech_starts), len(speech_ends))
            speech_starts = speech_starts[:min_len]
            speech_ends = speech_ends[:min_len]
            
            # 转换为时间并过滤短段
            for start_idx, end_idx in zip(speech_starts, speech_ends):
                start_time = times[start_idx] if start_idx < len(times) else times[-1]
                end_time = times[end_idx-1] if end_idx <= len(times) else times[-1]
                duration = end_time - start_time
                
                if duration >= min_speech_duration:
                    speech_segments.append({
                        'start': start_time,
                        'end': end_time,
                        'duration': duration,
                        'start_idx': start_idx,
                        'end_idx': end_idx
                    })
            
            # 检测停顿段
            for i in range(len(speech_segments) - 1):
                pause_start = speech_segments[i]['end']
                pause_end = speech_segments[i + 1]['start']
                pause_duration = pause_end - pause_start
                
                if pause_duration >= min_pause_duration:
                    pause_segments.append({
                        'start': pause_start,
                        'end': pause_end,
                        'duration': pause_duration
                    })
            
            # 计算统计信息
            total_speech_time = sum(seg['duration'] for seg in speech_segments)
            total_pause_time = sum(seg['duration'] for seg in pause_segments)
            total_time = len(audio_data) / self.sample_rate
            
            result = {
                'speech_segments': speech_segments,
                'pause_segments': pause_segments,
                'energy_threshold': adaptive_threshold,
                'statistics': {
                    'total_time': total_time,
                    'total_speech_time': total_speech_time,
                    'total_pause_time': total_pause_time,
                    'speech_ratio': total_speech_time / total_time if total_time > 0 else 0,
                    'pause_ratio': total_pause_time / total_time if total_time > 0 else 0,
                    'num_speech_segments': len(speech_segments),
                    'num_pauses': len(pause_segments),
                    'avg_speech_duration': total_speech_time / len(speech_segments) if speech_segments else 0,
                    'avg_pause_duration': total_pause_time / len(pause_segments) if pause_segments else 0
                }
            }
            
            return result
            
        except Exception as e:
            print(f"语音段检测失败: {e}")
            return {}
    
    def calculate_speech_rate(self, audio_data: np.ndarray, 
                             syllable_count: int) -> Dict[str, Any]:
        """
        计算语速
        
        Args:
            audio_data: 音频数据
            syllable_count: 音节数量
            
        Returns:
            Dict[str, Any]: 语速分析结果
        """
        try:
            # 检测语音段
            segments_data = self.detect_speech_segments(audio_data)
            if not segments_data:
                return {}
            
            stats = segments_data['statistics']
            
            # 计算不同的语速指标
            total_time = len(audio_data) / self.sample_rate
            speech_time = stats['total_speech_time']
            
            # 1. 总体语速（包含停顿）
            overall_rate = syllable_count / total_time if total_time > 0 else 0
            
            # 2. 净语速（不包含停顿）
            net_rate = syllable_count / speech_time if speech_time > 0 else 0
            
            # 3. 语速等级评估
            overall_level = self.classify_speech_rate(overall_rate)
            net_level = self.classify_speech_rate(net_rate)
            
            # 4. 语速稳定性分析
            if len(segments_data['speech_segments']) > 1:
                segment_rates = []
                for segment in segments_data['speech_segments']:
                    # 估算每个语音段的音节数（按时长比例分配）
                    segment_syllables = syllable_count * (segment['duration'] / speech_time)
                    segment_rate = segment_syllables / segment['duration'] if segment['duration'] > 0 else 0
                    segment_rates.append(segment_rate)
                
                rate_variation = variation(segment_rates) if len(segment_rates) > 1 else 0
                rate_stability = self.classify_rhythm_stability(rate_variation)
            else:
                rate_variation = 0
                rate_stability = 'stable'
            
            result = {
                'syllable_count': syllable_count,
                'total_time': total_time,
                'speech_time': speech_time,
                'overall_rate': overall_rate,
                'net_rate': net_rate,
                'overall_level': overall_level,
                'net_level': net_level,
                'rate_variation': rate_variation,
                'rate_stability': rate_stability,
                'standard_comparison': {
                    'overall_vs_standard': overall_rate / self.standard_speech_rate if self.standard_speech_rate > 0 else 0,
                    'net_vs_standard': net_rate / self.standard_speech_rate if self.standard_speech_rate > 0 else 0
                }
            }
            
            return result
            
        except Exception as e:
            print(f"语速计算失败: {e}")
            return {}
    
    def analyze_pauses(self, audio_data: np.ndarray, 
                      text: str = "") -> Dict[str, Any]:
        """
        分析停顿模式
        
        Args:
            audio_data: 音频数据
            text: 对应文本（用于分析停顿的合理性）
            
        Returns:
            Dict[str, Any]: 停顿分析结果
        """
        try:
            # 检测语音段和停顿
            segments_data = self.detect_speech_segments(audio_data)
            if not segments_data:
                return {}
            
            pause_segments = segments_data['pause_segments']
            
            # 停顿长度分类
            pause_classification = {
                'short_pauses': [],
                'medium_pauses': [],
                'long_pauses': [],
                'very_long_pauses': []
            }
            
            for pause in pause_segments:
                duration = pause['duration']
                if self.pause_standards['short_pause'][0] <= duration < self.pause_standards['short_pause'][1]:
                    pause_classification['short_pauses'].append(pause)
                elif self.pause_standards['medium_pause'][0] <= duration < self.pause_standards['medium_pause'][1]:
                    pause_classification['medium_pauses'].append(pause)
                elif self.pause_standards['long_pause'][0] <= duration < self.pause_standards['long_pause'][1]:
                    pause_classification['long_pauses'].append(pause)
                else:
                    pause_classification['very_long_pauses'].append(pause)
            
            # 停顿合理性分析（如果有文本）
            pause_appropriateness = self.analyze_pause_appropriateness(pause_segments, text) if text else {}
            
            # 停顿节奏分析
            if len(pause_segments) > 1:
                pause_intervals = []
                for i in range(len(pause_segments) - 1):
                    interval = pause_segments[i + 1]['start'] - pause_segments[i]['end']
                    pause_intervals.append(interval)
                
                rhythm_regularity = 1 - variation(pause_intervals) if len(pause_intervals) > 1 else 1
            else:
                rhythm_regularity = 1
            
            result = {
                'pause_segments': pause_segments,
                'pause_classification': pause_classification,
                'pause_appropriateness': pause_appropriateness,
                'rhythm_regularity': rhythm_regularity,
                'statistics': {
                    'total_pauses': len(pause_segments),
                    'pause_density': len(pause_segments) / (len(audio_data) / self.sample_rate),
                    'avg_pause_duration': np.mean([p['duration'] for p in pause_segments]) if pause_segments else 0,
                    'pause_duration_variation': variation([p['duration'] for p in pause_segments]) if len(pause_segments) > 1 else 0,
                    'pause_counts': {
                        'short': len(pause_classification['short_pauses']),
                        'medium': len(pause_classification['medium_pauses']),
                        'long': len(pause_classification['long_pauses']),
                        'very_long': len(pause_classification['very_long_pauses'])
                    }
                }
            }
            
            return result
            
        except Exception as e:
            print(f"停顿分析失败: {e}")
            return {}
    
    def analyze_pause_appropriateness(self, pause_segments: List[Dict], text: str) -> Dict[str, Any]:
        """
        分析停顿的合理性
        
        Args:
            pause_segments: 停顿段列表
            text: 文本内容
            
        Returns:
            Dict[str, Any]: 停顿合理性分析
        """
        try:
            if not text:
                return {}
            
            # 寻找文本中的标点符号位置
            punctuation_positions = []
            major_punct = r'[。！？]'
            minor_punct = r'[，、；：]'
            
            # 主要标点（应该有长停顿）
            for match in re.finditer(major_punct, text):
                punctuation_positions.append({
                    'position': match.start(),
                    'type': 'major',
                    'char': match.group()
                })
            
            # 次要标点（应该有短停顿）
            for match in re.finditer(minor_punct, text):
                punctuation_positions.append({
                    'position': match.start(),
                    'type': 'minor',
                    'char': match.group()
                })
            
            # 分析停顿与标点的对应关系
            # 这里简化处理，实际应该考虑字符与时间的对应关系
            appropriate_pauses = 0
            inappropriate_pauses = 0
            missing_pauses = 0
            
            # 评估标准：
            # 1. 主要标点后应该有中等到长的停顿
            # 2. 次要标点后应该有短到中等的停顿
            # 3. 没有标点的地方不应该有长停顿
            
            major_punct_count = len([p for p in punctuation_positions if p['type'] == 'major'])
            minor_punct_count = len([p for p in punctuation_positions if p['type'] == 'minor'])
            
            # 简化评估：比较停顿数量与标点数量
            total_punct = major_punct_count + minor_punct_count
            total_pauses = len(pause_segments)
            
            if total_punct > 0:
                pause_punct_ratio = total_pauses / total_punct
                if 0.7 <= pause_punct_ratio <= 1.3:
                    appropriateness_score = 0.8
                elif 0.5 <= pause_punct_ratio <= 1.5:
                    appropriateness_score = 0.6
                else:
                    appropriateness_score = 0.4
            else:
                appropriateness_score = 0.7 if total_pauses <= 2 else 0.5
            
            return {
                'punctuation_positions': punctuation_positions,
                'major_punct_count': major_punct_count,
                'minor_punct_count': minor_punct_count,
                'pause_punct_ratio': pause_punct_ratio if total_punct > 0 else 0,
                'appropriateness_score': appropriateness_score,
                'feedback': self.generate_pause_feedback(
                    major_punct_count, minor_punct_count, total_pauses, appropriateness_score
                )
            }
            
        except Exception as e:
            print(f"停顿合理性分析失败: {e}")
            return {}
    
    def analyze_rhythm_consistency(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        分析节奏一致性
        
        Args:
            audio_data: 音频数据
            
        Returns:
            Dict[str, Any]: 节奏一致性分析结果
        """
        try:
            # 检测语音段
            segments_data = self.detect_speech_segments(audio_data)
            if not segments_data or len(segments_data['speech_segments']) < 3:
                return {'error': '语音段太少，无法分析节奏'}
            
            speech_segments = segments_data['speech_segments']
            
            # 计算各种节奏指标
            segment_durations = [seg['duration'] for seg in speech_segments]
            pause_durations = [seg['duration'] for seg in segments_data['pause_segments']]
            
            # 1. 语音段长度的一致性
            duration_consistency = 1 - variation(segment_durations) if len(segment_durations) > 1 else 1
            
            # 2. 停顿长度的一致性
            pause_consistency = 1 - variation(pause_durations) if len(pause_durations) > 1 else 1
            
            # 3. 语音段间隔的规律性
            intervals = []
            for i in range(len(speech_segments) - 1):
                interval = speech_segments[i + 1]['start'] - speech_segments[i]['end']
                intervals.append(interval)
            
            interval_regularity = 1 - variation(intervals) if len(intervals) > 1 else 1
            
            # 4. 整体节奏评分
            rhythm_scores = [duration_consistency, pause_consistency, interval_regularity]
            overall_rhythm_score = np.mean(rhythm_scores)
            
            # 5. 节奏等级分类
            rhythm_level = self.classify_rhythm_stability(1 - overall_rhythm_score)
            
            result = {
                'segment_durations': segment_durations,
                'pause_durations': pause_durations,
                'intervals': intervals,
                'duration_consistency': duration_consistency,
                'pause_consistency': pause_consistency,
                'interval_regularity': interval_regularity,
                'overall_rhythm_score': overall_rhythm_score,
                'rhythm_level': rhythm_level,
                'statistics': {
                    'avg_segment_duration': np.mean(segment_durations),
                    'segment_duration_std': np.std(segment_durations),
                    'avg_pause_duration': np.mean(pause_durations) if pause_durations else 0,
                    'pause_duration_std': np.std(pause_durations) if pause_durations else 0,
                    'avg_interval': np.mean(intervals) if intervals else 0,
                    'interval_std': np.std(intervals) if intervals else 0
                }
            }
            
            return result
            
        except Exception as e:
            print(f"节奏一致性分析失败: {e}")
            return {}
    
    def evaluate_fluency(self, user_audio: np.ndarray,
                        standard_audio: np.ndarray,
                        text: str,
                        syllable_count: int) -> Dict[str, Any]:
        """
        综合评测流畅性
        
        Args:
            user_audio: 用户录音
            standard_audio: 标准录音
            text: 文本内容
            syllable_count: 音节数量
            
        Returns:
            Dict[str, Any]: 流畅性评测结果
        """
        try:
            # 1. 语速分析
            user_speech_rate = self.calculate_speech_rate(user_audio, syllable_count)
            standard_speech_rate = self.calculate_speech_rate(standard_audio, syllable_count)
            
            # 2. 停顿分析
            user_pauses = self.analyze_pauses(user_audio, text)
            standard_pauses = self.analyze_pauses(standard_audio, text)
            
            # 3. 节奏分析
            user_rhythm = self.analyze_rhythm_consistency(user_audio)
            standard_rhythm = self.analyze_rhythm_consistency(standard_audio)
            
            # 4. 计算各维度得分
            scores = {}
            
            # 语速得分
            if user_speech_rate and standard_speech_rate:
                rate_similarity = self.calculate_rate_similarity(
                    user_speech_rate, standard_speech_rate
                )
                scores['speech_rate'] = rate_similarity
            else:
                scores['speech_rate'] = 0
            
            # 停顿得分
            if user_pauses and standard_pauses:
                pause_similarity = self.calculate_pause_similarity(
                    user_pauses, standard_pauses
                )
                scores['pauses'] = pause_similarity
            else:
                scores['pauses'] = 0
            
            # 节奏得分
            if user_rhythm and standard_rhythm:
                rhythm_similarity = self.calculate_rhythm_similarity(
                    user_rhythm, standard_rhythm
                )
                scores['rhythm'] = rhythm_similarity
            else:
                scores['rhythm'] = 0
            
            # 停顿合理性得分
            if user_pauses and 'pause_appropriateness' in user_pauses:
                appropriateness = user_pauses['pause_appropriateness'].get('appropriateness_score', 0)
                scores['appropriateness'] = appropriateness
            else:
                scores['appropriateness'] = 0.5
            
            # 5. 综合评分
            weights = {
                'speech_rate': 0.3,
                'pauses': 0.25,
                'rhythm': 0.25,
                'appropriateness': 0.2
            }
            
            total_score = 0
            total_weight = 0
            
            for aspect, score in scores.items():
                weight = weights.get(aspect, 0)
                if weight > 0 and not np.isnan(score):
                    total_score += score * weight
                    total_weight += weight
            
            final_score = (total_score / total_weight) * 100 if total_weight > 0 else 0
            
            # 6. 生成详细反馈
            feedback = self.generate_fluency_feedback(
                user_speech_rate, user_pauses, user_rhythm, scores
            )
            
            result = {
                'score': min(100, max(0, final_score)),
                'dimension_scores': {k: v * 100 for k, v in scores.items()},
                'user_analysis': {
                    'speech_rate': user_speech_rate,
                    'pauses': user_pauses,
                    'rhythm': user_rhythm
                },
                'standard_analysis': {
                    'speech_rate': standard_speech_rate,
                    'pauses': standard_pauses,
                    'rhythm': standard_rhythm
                },
                'feedback': feedback
            }
            
            return result
            
        except Exception as e:
            print(f"流畅性评测失败: {e}")
            return {'score': 0, 'error': str(e)}
    
    def calculate_rate_similarity(self, user_rate: Dict, standard_rate: Dict) -> float:
        """计算语速相似度"""
        try:
            if not user_rate or not standard_rate:
                return 0.0
            
            # 比较净语速
            user_net = user_rate.get('net_rate', 0)
            standard_net = standard_rate.get('net_rate', 0)
            
            if standard_net == 0:
                return 0.5
            
            # 计算相对误差
            rate_error = abs(user_net - standard_net) / standard_net
            
            # 容忍度为30%
            similarity = max(0, 1 - rate_error / 0.3)
            
            # 语速稳定性也考虑在内
            user_stability = user_rate.get('rate_variation', 1)
            standard_stability = standard_rate.get('rate_variation', 1)
            
            stability_similarity = 1 - abs(user_stability - standard_stability) / max(user_stability, standard_stability, 1)
            
            # 综合得分
            return 0.7 * similarity + 0.3 * stability_similarity
            
        except Exception as e:
            print(f"语速相似度计算失败: {e}")
            return 0.0
    
    def calculate_pause_similarity(self, user_pauses: Dict, standard_pauses: Dict) -> float:
        """计算停顿相似度"""
        try:
            if not user_pauses or not standard_pauses:
                return 0.0
            
            user_stats = user_pauses.get('statistics', {})
            standard_stats = standard_pauses.get('statistics', {})
            
            similarities = []
            
            # 停顿数量相似度
            user_count = user_stats.get('total_pauses', 0)
            standard_count = standard_stats.get('total_pauses', 0)
            if max(user_count, standard_count) > 0:
                count_sim = 1 - abs(user_count - standard_count) / max(user_count, standard_count)
                similarities.append(count_sim)
            
            # 平均停顿时长相似度
            user_avg = user_stats.get('avg_pause_duration', 0)
            standard_avg = standard_stats.get('avg_pause_duration', 0)
            if max(user_avg, standard_avg) > 0:
                duration_sim = 1 - abs(user_avg - standard_avg) / max(user_avg, standard_avg)
                similarities.append(duration_sim)
            
            # 停顿分布相似度
            user_counts = user_stats.get('pause_counts', {})
            standard_counts = standard_stats.get('pause_counts', {})
            
            for pause_type in ['short', 'medium', 'long', 'very_long']:
                user_type_count = user_counts.get(pause_type, 0)
                standard_type_count = standard_counts.get(pause_type, 0)
                if max(user_type_count, standard_type_count) > 0:
                    type_sim = 1 - abs(user_type_count - standard_type_count) / max(user_type_count, standard_type_count)
                    similarities.append(type_sim * 0.5)  # 降低权重
            
            return np.mean(similarities) if similarities else 0.0
            
        except Exception as e:
            print(f"停顿相似度计算失败: {e}")
            return 0.0
    
    def calculate_rhythm_similarity(self, user_rhythm: Dict, standard_rhythm: Dict) -> float:
        """计算节奏相似度"""
        try:
            if not user_rhythm or not standard_rhythm:
                return 0.0
            
            user_score = user_rhythm.get('overall_rhythm_score', 0)
            standard_score = standard_rhythm.get('overall_rhythm_score', 0)
            
            # 比较整体节奏得分
            if max(user_score, standard_score) > 0:
                similarity = 1 - abs(user_score - standard_score) / max(user_score, standard_score)
            else:
                similarity = 0.5
            
            return max(0, similarity)
            
        except Exception as e:
            print(f"节奏相似度计算失败: {e}")
            return 0.0
    
    def classify_speech_rate(self, rate: float) -> str:
        """分类语速等级"""
        for level, (min_rate, max_rate) in self.speech_rate_standards.items():
            if min_rate <= rate < max_rate:
                return level
        return 'unknown'
    
    def classify_rhythm_stability(self, variation_value: float) -> str:
        """分类节奏稳定性"""
        for level, (min_var, max_var) in self.rhythm_standards.items():
            if min_var <= variation_value < max_var:
                return level
        return 'unknown'
    
    def generate_pause_feedback(self, major_punct: int, minor_punct: int, 
                               total_pauses: int, appropriateness: float) -> str:
        """生成停顿反馈"""
        feedback = []
        
        total_punct = major_punct + minor_punct
        
        if appropriateness > 0.8:
            feedback.append("停顿位置很合理！")
        elif appropriateness > 0.6:
            feedback.append("停顿位置基本合理。")
        else:
            feedback.append("停顿位置需要改进。")
        
        if total_punct > 0:
            if total_pauses < total_punct * 0.5:
                feedback.append("缺少必要的停顿，注意在标点符号处适当停顿。")
            elif total_pauses > total_punct * 1.5:
                feedback.append("停顿过多，影响语言流畅性。")
        
        return " ".join(feedback)
    
    def generate_fluency_feedback(self, speech_rate: Dict, pauses: Dict, 
                                 rhythm: Dict, scores: Dict) -> str:
        """生成流畅性反馈"""
        try:
            feedback = []
            
            # 总体评价
            avg_score = np.mean(list(scores.values()))
            if avg_score > 0.8:
                feedback.append("语言流畅性很好！")
            elif avg_score > 0.6:
                feedback.append("语言流畅性良好，还有改进空间。")
            else:
                feedback.append("语言流畅性需要加强。")
            
            # 语速反馈
            if speech_rate:
                rate_level = speech_rate.get('net_level', 'unknown')
                if rate_level == 'very_slow':
                    feedback.append("语速偏慢，可以适当加快。")
                elif rate_level == 'very_fast':
                    feedback.append("语速偏快，注意控制节奏。")
                elif rate_level == 'normal':
                    feedback.append("语速适中。")
                
                stability = speech_rate.get('rate_stability', 'unknown')
                if stability in ['unstable', 'very_unstable']:
                    feedback.append("语速变化较大，注意保持稳定。")
            
            # 停顿反馈
            if pauses and 'pause_appropriateness' in pauses:
                pause_feedback = pauses['pause_appropriateness'].get('feedback', '')
                if pause_feedback:
                    feedback.append(pause_feedback)
            
            # 节奏反馈
            if rhythm:
                rhythm_level = rhythm.get('rhythm_level', 'unknown')
                if rhythm_level in ['unstable', 'very_unstable']:
                    feedback.append("说话节奏不够规律，注意保持一致性。")
                elif rhythm_level in ['very_stable', 'stable']:
                    feedback.append("说话节奏很好。")
            
            # 具体建议
            if scores.get('speech_rate', 0) < 0.6:
                feedback.append("建议：练习控制语速，保持稳定的说话节奏。")
            
            if scores.get('pauses', 0) < 0.6:
                feedback.append("建议：注意在标点符号处适当停顿，提高表达清晰度。")
            
            if scores.get('rhythm', 0) < 0.6:
                feedback.append("建议：练习朗读，培养稳定的语言节奏感。")
            
            return " ".join(feedback)
            
        except Exception as e:
            print(f"生成流畅性反馈失败: {e}")
            return "流畅性有待提高，建议多加练习。"

# 测试代码
if __name__ == "__main__":
    evaluator = FluencyEvaluator()
    
    # 创建测试音频
    import librosa
    test_audio_path = "/mnt/e/Code/Outsourcing/普通话测评/mandarin_evaluation/data/音频/01.MP3"
    
    try:
        audio_data, sr = librosa.load(test_audio_path, sr=16000)
        print(f"加载测试音频: {len(audio_data)/sr:.2f}秒")
        
        # 测试语音段检测
        segments = evaluator.detect_speech_segments(audio_data)
        if segments:
            print(f"检测到 {len(segments['speech_segments'])} 个语音段")
            print(f"语音比例: {segments['statistics']['speech_ratio']:.2f}")
        
        # 测试语速计算
        test_syllable_count = 50  # 假设50个音节
        speech_rate = evaluator.calculate_speech_rate(audio_data, test_syllable_count)
        if speech_rate:
            print(f"语速: {speech_rate['overall_rate']:.2f} 音节/秒")
            print(f"语速等级: {speech_rate['overall_level']}")
        
        # 测试流畅性评测
        test_text = "这是一个测试文本，用来评测流畅性。"
        fluency_result = evaluator.evaluate_fluency(
            audio_data, audio_data, test_text, test_syllable_count
        )
        print(f"流畅性评分: {fluency_result['score']:.1f}%")
        print(f"反馈: {fluency_result['feedback']}")
        
    except Exception as e:
        print(f"测试失败: {e}")