#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼音分析和文本处理模块
处理中文文本的拼音转换、声韵调分离等功能
"""

import re
from typing import List, Dict, Tuple, Any
from pypinyin import pinyin, lazy_pinyin, Style
import json
from pathlib import Path

class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        """初始化文本处理器"""
        # 声母表
        self.initials = {
            'b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 'g', 'k', 'h', 
            'j', 'q', 'x', 'zh', 'ch', 'sh', 'r', 'z', 'c', 's', 'y', 'w'
        }
        
        # 韵母表  
        self.finals = {
            'a', 'o', 'e', 'i', 'u', 'v', 'ai', 'ei', 'ui', 'ao', 'ou', 'iu',
            'ie', 've', 'er', 'an', 'en', 'in', 'un', 'vn', 'ang', 'eng', 'ing',
            'ong', 'uai', 'uei', 'uan', 'uen', 'uang', 'ueng', 'iao', 'ian',
            'iang', 'iong', 'ua', 'uo', 'ue', 'ui'
        }
        
        # 声调标记
        self.tone_marks = {
            1: '¯',  # 第一声
            2: '´',  # 第二声  
            3: 'ˇ',  # 第三声
            4: '`',  # 第四声
            0: ''    # 轻声
        }
        
        # 加载多音字词典(如果有的话)
        self.load_polyphone_dict()
    
    def load_polyphone_dict(self):
        """加载多音字词典"""
        try:
            # 这里可以加载自定义的多音字词典
            # 暂时使用空字典，依赖pypinyin的默认处理
            self.polyphone_dict = {}
        except Exception as e:
            print(f"加载多音字词典失败: {e}")
            self.polyphone_dict = {}
    
    def text_to_pinyin(self, text: str, with_tone: bool = True) -> List[str]:
        """
        将中文文本转换为拼音
        
        Args:
            text: 中文文本
            with_tone: 是否包含声调
            
        Returns:
            List[str]: 拼音列表
        """
        try:
            # 清理文本，只保留中文字符
            clean_text = re.sub(r'[^\u4e00-\u9fff]', '', text)
            
            if with_tone:
                # 带声调的拼音
                result = pinyin(clean_text, style=Style.TONE3, errors='ignore')
            else:
                # 不带声调的拼音
                result = pinyin(clean_text, style=Style.NORMAL, errors='ignore')
            
            # 展平结果
            pinyin_list = [item[0] for item in result if item]
            
            return pinyin_list
            
        except Exception as e:
            print(f"拼音转换失败: {e}")
            return []
    
    def analyze_pinyin(self, pinyin_str: str) -> Dict[str, Any]:
        """
        分析单个拼音的声母、韵母、声调
        
        Args:
            pinyin_str: 拼音字符串，如 "zhang1"
            
        Returns:
            Dict[str, Any]: 包含声母、韵母、声调的字典
        """
        try:
            result = {
                'original': pinyin_str,
                'initial': '',
                'final': '',
                'tone': 0
            }
            
            # 提取声调
            tone_match = re.search(r'[0-4]$', pinyin_str)
            if tone_match:
                result['tone'] = int(tone_match.group())
                base_pinyin = pinyin_str[:-1]  # 去掉声调数字
            else:
                base_pinyin = pinyin_str
            
            # 分离声母和韵母
            # 先匹配双字母声母（zh, ch, sh）
            if base_pinyin.startswith(('zh', 'ch', 'sh')):
                result['initial'] = base_pinyin[:2]
                result['final'] = base_pinyin[2:]
            # 再匹配单字母声母
            elif base_pinyin[0] in self.initials:
                result['initial'] = base_pinyin[0]
                result['final'] = base_pinyin[1:]
            # 零声母情况
            else:
                result['initial'] = ''
                result['final'] = base_pinyin
            
            # 处理特殊韵母
            if result['final'] == 'i' and result['initial'] in ['zh', 'ch', 'sh', 'r', 'z', 'c', 's']:
                # zh, ch, sh, r, z, c, s 后的 i 实际读音不同
                result['final'] = 'i_special'
            
            return result
            
        except Exception as e:
            print(f"拼音分析失败: {e}")
            return {'original': pinyin_str, 'initial': '', 'final': '', 'tone': 0}
    
    def process_text(self, text: str) -> Dict[str, Any]:
        """
        完整处理文本，返回详细的分析结果
        
        Args:
            text: 中文文本
            
        Returns:
            Dict[str, Any]: 包含完整分析结果的字典
        """
        try:
            # 清理文本
            clean_text = re.sub(r'[^\u4e00-\u9fff，。、；：！？""''（）《》【】]', '', text)
            
            # 分字符处理
            characters = list(clean_text)
            
            # 获取拼音
            pinyin_list = self.text_to_pinyin(clean_text, with_tone=True)
            
            # 构建结果
            result = {
                'original_text': text,
                'clean_text': clean_text,
                'character_count': len([c for c in characters if '\u4e00' <= c <= '\u9fff']),
                'characters': [],
                'syllables': [],
                'initials': [],
                'finals': [],
                'tones': [],
                'statistics': {}
            }
            
            # 逐字符分析
            char_index = 0
            for char in characters:
                if '\u4e00' <= char <= '\u9fff':  # 只处理中文字符
                    if char_index < len(pinyin_list):
                        py = pinyin_list[char_index]
                        analysis = self.analyze_pinyin(py)
                        
                        result['characters'].append(char)
                        result['syllables'].append(py)
                        result['initials'].append(analysis['initial'])
                        result['finals'].append(analysis['final'])
                        result['tones'].append(analysis['tone'])
                        
                        char_index += 1
            
            # 计算统计信息
            result['statistics'] = self.calculate_statistics(result)
            
            return result
            
        except Exception as e:
            print(f"文本处理失败: {e}")
            return {}
    
    def calculate_statistics(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算文本的统计信息
        
        Args:
            analysis_result: 文本分析结果
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {}
            
            # 声母统计
            initial_count = {}
            for initial in analysis_result['initials']:
                if initial:  # 排除零声母
                    initial_count[initial] = initial_count.get(initial, 0) + 1
            stats['initial_distribution'] = initial_count
            stats['zero_initial_count'] = analysis_result['initials'].count('')
            
            # 韵母统计
            final_count = {}
            for final in analysis_result['finals']:
                if final:
                    final_count[final] = final_count.get(final, 0) + 1
            stats['final_distribution'] = final_count
            
            # 声调统计
            tone_count = {}
            for tone in analysis_result['tones']:
                tone_count[tone] = tone_count.get(tone, 0) + 1
            stats['tone_distribution'] = tone_count
            
            # 音节复杂度统计
            syllable_lengths = [len(syl.replace('1', '').replace('2', '').replace('3', '').replace('4', '').replace('0', '')) 
                               for syl in analysis_result['syllables']]
            stats['avg_syllable_length'] = sum(syllable_lengths) / len(syllable_lengths) if syllable_lengths else 0
            
            return stats
            
        except Exception as e:
            print(f"统计计算失败: {e}")
            return {}
    
    def get_pronunciation_difficulty(self, text: str) -> Dict[str, Any]:
        """
        评估文本的发音难度
        
        Args:
            text: 中文文本
            
        Returns:
            Dict[str, Any]: 难度评估结果
        """
        try:
            analysis = self.process_text(text)
            
            difficulty = {
                'overall_difficulty': 'medium',
                'difficult_initials': [],
                'difficult_finals': [],
                'difficult_tones': [],
                'difficulty_score': 0.0,
                'suggestions': []
            }
            
            # 难发音的声母
            hard_initials = ['zh', 'ch', 'sh', 'r', 'j', 'q', 'x']
            difficulty['difficult_initials'] = [
                initial for initial in analysis['initials'] 
                if initial in hard_initials
            ]
            
            # 难发音的韵母
            hard_finals = ['üe', 've', 'vn', 'iong', 'uang', 'ueng']
            difficulty['difficult_finals'] = [
                final for final in analysis['finals']
                if final in hard_finals
            ]
            
            # 第三声比较难掌握
            tone3_count = analysis['statistics']['tone_distribution'].get(3, 0)
            if tone3_count > len(analysis['tones']) * 0.3:
                difficulty['difficult_tones'].append('第三声较多')
            
            # 计算难度分数 (0-10)
            score = 5.0  # 基础分数
            
            # 根据难音增加分数
            score += len(difficulty['difficult_initials']) * 0.2
            score += len(difficulty['difficult_finals']) * 0.3
            score += tone3_count * 0.1
            
            # 根据长度调整
            if analysis['character_count'] > 100:
                score += 1.0
            elif analysis['character_count'] < 20:
                score -= 0.5
            
            difficulty['difficulty_score'] = min(10.0, max(0.0, score))
            
            # 确定难度等级
            if difficulty['difficulty_score'] < 3:
                difficulty['overall_difficulty'] = 'easy'
            elif difficulty['difficulty_score'] < 7:
                difficulty['overall_difficulty'] = 'medium'
            else:
                difficulty['overall_difficulty'] = 'hard'
            
            # 生成建议
            suggestions = []
            if len(difficulty['difficult_initials']) > 5:
                suggestions.append("注意翘舌音和平舌音的区别")
            if len(difficulty['difficult_finals']) > 3:
                suggestions.append("重点练习复合韵母的发音")
            if tone3_count > 0:
                suggestions.append("注意第三声的完整发音")
            if analysis['character_count'] > 80:
                suggestions.append("文本较长，注意语速和停顿")
            
            difficulty['suggestions'] = suggestions
            
            return difficulty
            
        except Exception as e:
            print(f"难度评估失败: {e}")
            return {'overall_difficulty': 'unknown', 'difficulty_score': 0.0}
    
    def segment_by_punctuation(self, text: str) -> List[str]:
        """
        根据标点符号分割文本
        
        Args:
            text: 文本
            
        Returns:
            List[str]: 分割后的文本段落
        """
        try:
            # 定义分割标点
            major_punct = r'[。！？]'
            minor_punct = r'[，、；：]'
            
            # 先按主要标点分割
            major_segments = re.split(major_punct, text)
            
            # 再对每段按次要标点分割
            all_segments = []
            for segment in major_segments:
                if segment.strip():
                    minor_segments = re.split(minor_punct, segment)
                    all_segments.extend([s.strip() for s in minor_segments if s.strip()])
            
            return all_segments
            
        except Exception as e:
            print(f"文本分割失败: {e}")
            return [text]
    
    def create_pronunciation_guide(self, text: str) -> str:
        """
        创建发音指导文本
        
        Args:
            text: 中文文本
            
        Returns:
            str: 包含拼音标注的指导文本
        """
        try:
            analysis = self.process_text(text)
            
            guide_lines = []
            guide_lines.append("发音指导:")
            guide_lines.append("=" * 40)
            
            # 逐字符生成指导
            for i, char in enumerate(analysis['characters']):
                if i < len(analysis['syllables']):
                    syllable = analysis['syllables'][i]
                    initial = analysis['initials'][i]
                    final = analysis['finals'][i]
                    tone = analysis['tones'][i]
                    
                    guide_line = f"{char} [{syllable}]"
                    
                    # 添加声母韵母分解
                    if initial:
                        guide_line += f" = {initial} + {final}"
                    else:
                        guide_line += f" = (零声母) + {final}"
                    
                    # 添加声调说明
                    tone_names = {1: "第一声", 2: "第二声", 3: "第三声", 4: "第四声", 0: "轻声"}
                    guide_line += f" ({tone_names.get(tone, '未知声调')})"
                    
                    guide_lines.append(guide_line)
            
            # 添加难点提示
            difficulty = self.get_pronunciation_difficulty(text)
            if difficulty['suggestions']:
                guide_lines.append("\n重点注意:")
                for suggestion in difficulty['suggestions']:
                    guide_lines.append(f"• {suggestion}")
            
            return "\n".join(guide_lines)
            
        except Exception as e:
            print(f"创建发音指导失败: {e}")
            return text

# 工具函数
def load_standard_texts(file_path: str) -> Dict[str, str]:
    """
    加载标准文本数据
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        Dict[str, str]: 文本ID到文本内容的映射
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载标准文本失败: {e}")
        return {}

# 测试代码
if __name__ == "__main__":
    processor = TextProcessor()
    
    # 测试文本
    test_text = "春天来了，万物复苏。"
    
    print("=== 文本处理测试 ===")
    
    # 拼音转换测试
    pinyins = processor.text_to_pinyin(test_text)
    print(f"拼音转换: {pinyins}")
    
    # 拼音分析测试
    for py in pinyins:
        analysis = processor.analyze_pinyin(py)
        print(f"拼音分析 {py}: {analysis}")
    
    # 完整文本处理测试
    result = processor.process_text(test_text)
    print(f"\n完整分析:")
    print(f"字符数: {result['character_count']}")
    print(f"拼音: {result['syllables']}")
    print(f"统计: {result['statistics']}")
    
    # 难度评估测试
    difficulty = processor.get_pronunciation_difficulty(test_text)
    print(f"\n难度评估: {difficulty}")
    
    # 发音指导测试
    guide = processor.create_pronunciation_guide(test_text)
    print(f"\n{guide}")