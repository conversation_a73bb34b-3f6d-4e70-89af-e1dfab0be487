#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
声母韵母评测模块
基于声学特征分析实现声母和韵母的发音准确度评测
"""

import numpy as np
import librosa
from scipy import signal
from scipy.spatial.distance import cosine
from scipy.stats import pearsonr
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

class PhonemeEvaluator:
    """声母韵母评测器"""
    
    def __init__(self, sample_rate: int = 16000):
        """
        初始化评测器
        
        Args:
            sample_rate: 采样率
        """
        self.sample_rate = sample_rate
        self.frame_length = int(0.025 * sample_rate)  # 25ms窗口
        self.hop_length = int(0.01 * sample_rate)     # 10ms步长
        
        # 初始化声母韵母特征模板
        self.initialize_phoneme_templates()
    
    def initialize_phoneme_templates(self):
        """初始化声母韵母特征模板"""
        # 声母频谱特征模板（基于典型频谱特征）
        self.initial_templates = {
            # 塞音
            'b': {'formant_pattern': [500, 1500, 2500], 'energy_pattern': 'burst'},
            'p': {'formant_pattern': [600, 1600, 2600], 'energy_pattern': 'burst'},
            'd': {'formant_pattern': [700, 1700, 2700], 'energy_pattern': 'burst'},
            't': {'formant_pattern': [800, 1800, 2800], 'energy_pattern': 'burst'},
            'g': {'formant_pattern': [900, 1900, 2900], 'energy_pattern': 'burst'},
            'k': {'formant_pattern': [1000, 2000, 3000], 'energy_pattern': 'burst'},
            
            # 塞擦音
            'j': {'formant_pattern': [300, 2100, 3200], 'energy_pattern': 'fricative'},
            'q': {'formant_pattern': [400, 2200, 3300], 'energy_pattern': 'fricative'},
            'zh': {'formant_pattern': [500, 1200, 2400], 'energy_pattern': 'fricative'},
            'ch': {'formant_pattern': [600, 1300, 2500], 'energy_pattern': 'fricative'},
            'z': {'formant_pattern': [700, 1800, 2800], 'energy_pattern': 'fricative'},
            'c': {'formant_pattern': [800, 1900, 2900], 'energy_pattern': 'fricative'},
            
            # 擦音
            'f': {'formant_pattern': [1000, 2500, 3500], 'energy_pattern': 'fricative'},
            'h': {'formant_pattern': [1200, 2000, 3000], 'energy_pattern': 'fricative'},
            'x': {'formant_pattern': [1500, 2300, 3200], 'energy_pattern': 'fricative'},
            'sh': {'formant_pattern': [1300, 2100, 2800], 'energy_pattern': 'fricative'},
            's': {'formant_pattern': [1600, 2400, 3200], 'energy_pattern': 'fricative'},
            'r': {'formant_pattern': [1100, 1400, 2200], 'energy_pattern': 'fricative'},
            
            # 鼻音
            'm': {'formant_pattern': [250, 1000, 2200], 'energy_pattern': 'nasal'},
            'n': {'formant_pattern': [280, 1100, 2300], 'energy_pattern': 'nasal'},
            
            # 边音
            'l': {'formant_pattern': [350, 1200, 2500], 'energy_pattern': 'lateral'},
            
            # 半元音
            'w': {'formant_pattern': [300, 800, 2200], 'energy_pattern': 'glide'},
            'y': {'formant_pattern': [280, 2200, 3100], 'energy_pattern': 'glide'},
        }
        
        # 韵母共振峰模板
        self.final_templates = {
            # 单韵母
            'a': {'f1': 730, 'f2': 1090, 'f3': 2440},
            'o': {'f1': 570, 'f2': 840, 'f3': 2410},
            'e': {'f1': 610, 'f2': 1900, 'f3': 2480},
            'i': {'f1': 270, 'f2': 2290, 'f3': 3010},
            'u': {'f1': 300, 'f2': 870, 'f3': 2240},
            'v': {'f1': 320, 'f2': 1520, 'f3': 2290},
            
            # 复韵母
            'ai': {'f1': [730, 270], 'f2': [1090, 2290], 'f3': [2440, 3010]},
            'ei': {'f1': [610, 270], 'f2': [1900, 2290], 'f3': [2480, 3010]},
            'ao': {'f1': [730, 570], 'f2': [1090, 840], 'f3': [2440, 2410]},
            'ou': {'f1': [570, 300], 'f2': [840, 870], 'f3': [2410, 2240]},
            
            # 鼻韵母
            'an': {'f1': 730, 'f2': 1090, 'f3': 2440, 'nasal': True},
            'en': {'f1': 610, 'f2': 1900, 'f3': 2480, 'nasal': True},
            'in': {'f1': 270, 'f2': 2290, 'f3': 3010, 'nasal': True},
            'un': {'f1': 300, 'f2': 870, 'f3': 2240, 'nasal': True},
            'ang': {'f1': 730, 'f2': 1090, 'f3': 2440, 'nasal': True},
            'eng': {'f1': 610, 'f2': 1900, 'f3': 2480, 'nasal': True},
            'ing': {'f1': 270, 'f2': 2290, 'f3': 3010, 'nasal': True},
            'ong': {'f1': 570, 'f2': 840, 'f3': 2410, 'nasal': True},
        }
    
    def extract_phoneme_features(self, audio_data: np.ndarray, 
                                start_time: float = 0, 
                                end_time: Optional[float] = None) -> Dict[str, Any]:
        """
        提取音素的声学特征
        
        Args:
            audio_data: 音频数据
            start_time: 开始时间（秒）
            end_time: 结束时间（秒），None表示到音频结尾
            
        Returns:
            Dict[str, Any]: 声学特征字典
        """
        try:
            # 计算时间索引
            start_idx = int(start_time * self.sample_rate)
            end_idx = int(end_time * self.sample_rate) if end_time else len(audio_data)
            
            # 提取音频片段
            segment = audio_data[start_idx:end_idx]
            
            if len(segment) < self.frame_length:
                # 音频片段太短，填充零
                segment = np.pad(segment, (0, self.frame_length - len(segment)))
            
            features = {}
            
            # 1. MFCC特征
            mfcc = librosa.feature.mfcc(y=segment, sr=self.sample_rate, n_mfcc=13)
            features['mfcc'] = np.mean(mfcc, axis=1)
            features['mfcc_var'] = np.var(mfcc, axis=1)
            
            # 2. 梅尔频谱
            mel_spec = librosa.feature.melspectrogram(y=segment, sr=self.sample_rate)
            features['mel_mean'] = np.mean(mel_spec, axis=1)
            features['mel_var'] = np.var(mel_spec, axis=1)
            
            # 3. 共振峰频率
            formants = self.extract_formants(segment)
            features['formants'] = formants
            
            # 4. 频谱质心和带宽
            spectral_centroids = librosa.feature.spectral_centroid(y=segment, sr=self.sample_rate)
            features['spectral_centroid'] = np.mean(spectral_centroids)
            
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=segment, sr=self.sample_rate)
            features['spectral_bandwidth'] = np.mean(spectral_bandwidth)
            
            # 5. 零交叉率
            zcr = librosa.feature.zero_crossing_rate(segment)
            features['zcr'] = np.mean(zcr)
            
            # 6. 短时能量
            energy = np.sum(segment ** 2) / len(segment)
            features['energy'] = energy
            
            # 7. 频谱滚降点
            spectral_rolloff = librosa.feature.spectral_rolloff(y=segment, sr=self.sample_rate)
            features['spectral_rolloff'] = np.mean(spectral_rolloff)
            
            # 8. 色度特征
            chroma = librosa.feature.chroma_stft(y=segment, sr=self.sample_rate)
            features['chroma'] = np.mean(chroma, axis=1)
            
            return features
            
        except Exception as e:
            print(f"特征提取失败: {e}")
            return {}
    
    def extract_formants(self, audio_data: np.ndarray, max_formants: int = 4) -> List[float]:
        """
        提取共振峰频率
        
        Args:
            audio_data: 音频数据
            max_formants: 最大共振峰数量
            
        Returns:
            List[float]: 共振峰频率列表
        """
        try:
            # 预处理：预加重
            pre_emphasis = 0.97
            emphasized = np.append(audio_data[0], audio_data[1:] - pre_emphasis * audio_data[:-1])
            
            # 窗函数
            windowed = emphasized * np.hamming(len(emphasized))
            
            # 自相关函数
            autocorr = np.correlate(windowed, windowed, mode='full')
            autocorr = autocorr[len(autocorr)//2:]
            
            # LPC分析
            try:
                # 使用简单的Burg方法估计
                order = min(12, len(autocorr)//3)
                if order < 2:
                    return [0.0] * max_formants
                
                # 计算LPC系数
                lpc_coeffs = self.lpc_analysis(windowed, order)
                
                # 从LPC系数计算共振峰
                formants = self.lpc_to_formants(lpc_coeffs, self.sample_rate)
                
                # 确保返回指定数量的共振峰
                while len(formants) < max_formants:
                    formants.append(0.0)
                
                return formants[:max_formants]
                
            except:
                # 如果LPC分析失败，使用FFT峰值检测
                return self.fft_formant_estimation(windowed, max_formants)
            
        except Exception as e:
            print(f"共振峰提取失败: {e}")
            return [0.0] * max_formants
    
    def lpc_analysis(self, signal_data: np.ndarray, order: int) -> np.ndarray:
        """
        线性预测编码分析
        
        Args:
            signal_data: 信号数据
            order: LPC阶数
            
        Returns:
            np.ndarray: LPC系数
        """
        try:
            # 自相关函数
            R = np.correlate(signal_data, signal_data, mode='full')
            R = R[len(R)//2:len(R)//2 + order + 1]
            
            if len(R) < order + 1:
                return np.zeros(order + 1)
            
            # Levinson-Durbin算法
            a = np.zeros(order + 1)
            a[0] = 1.0
            
            if R[0] == 0:
                return a
            
            E = R[0]
            
            for i in range(1, order + 1):
                if E == 0:
                    break
                    
                k = -np.sum(a[:i] * R[i:0:-1]) / E
                
                a_new = np.zeros(i + 1)
                a_new[0] = 1.0
                a_new[i] = k
                
                for j in range(1, i):
                    a_new[j] = a[j] + k * a[i-j]
                
                a = a_new.copy()
                E = E * (1 - k * k)
            
            return a
            
        except Exception as e:
            print(f"LPC分析失败: {e}")
            return np.zeros(order + 1)
    
    def lpc_to_formants(self, lpc_coeffs: np.ndarray, sample_rate: int) -> List[float]:
        """
        从LPC系数计算共振峰频率
        
        Args:
            lpc_coeffs: LPC系数
            sample_rate: 采样率
            
        Returns:
            List[float]: 共振峰频率列表
        """
        try:
            # 计算多项式的根
            roots = np.roots(lpc_coeffs)
            
            # 只保留复数根
            roots = roots[np.imag(roots) > 0]
            
            # 计算角频率
            angles = np.angle(roots)
            
            # 转换为频率
            frequencies = angles * sample_rate / (2 * np.pi)
            
            # 排序并过滤
            frequencies = frequencies[frequencies > 50]  # 去除过低的频率
            frequencies = frequencies[frequencies < sample_rate/2]  # 去除过高的频率
            frequencies = np.sort(frequencies)
            
            return frequencies.tolist()
            
        except Exception as e:
            print(f"共振峰计算失败: {e}")
            return []
    
    def fft_formant_estimation(self, signal_data: np.ndarray, max_formants: int) -> List[float]:
        """
        基于FFT的共振峰估计（备用方法）
        
        Args:
            signal_data: 信号数据
            max_formants: 最大共振峰数量
            
        Returns:
            List[float]: 共振峰频率列表
        """
        try:
            # FFT
            fft = np.fft.fft(signal_data)
            magnitude = np.abs(fft[:len(fft)//2])
            
            # 频率轴
            freqs = np.fft.fftfreq(len(signal_data), 1/self.sample_rate)[:len(fft)//2]
            
            # 平滑频谱
            from scipy import ndimage
            smoothed = ndimage.gaussian_filter1d(magnitude, sigma=2)
            
            # 寻找峰值
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(smoothed, height=np.max(smoothed)*0.1, distance=20)
            
            # 获取峰值对应的频率
            formant_freqs = freqs[peaks]
            
            # 过滤合理范围内的频率
            formant_freqs = formant_freqs[(formant_freqs > 200) & (formant_freqs < 4000)]
            
            # 排序并取前几个
            formant_freqs = np.sort(formant_freqs)[:max_formants]
            
            # 补齐到指定数量
            result = formant_freqs.tolist()
            while len(result) < max_formants:
                result.append(0.0)
            
            return result
            
        except Exception as e:
            print(f"FFT共振峰估计失败: {e}")
            return [0.0] * max_formants
    
    def evaluate_initial(self, user_audio: np.ndarray, 
                        standard_audio: np.ndarray,
                        target_initial: str,
                        syllable_duration: float = 0.5) -> Dict[str, Any]:
        """
        评测声母发音
        
        Args:
            user_audio: 用户录音
            standard_audio: 标准录音
            target_initial: 目标声母
            syllable_duration: 音节持续时间
            
        Returns:
            Dict[str, Any]: 声母评测结果
        """
        try:
            # 提取声母部分（前30%的音节时间）
            initial_duration = syllable_duration * 0.3
            
            # 提取用户音频的声母特征
            user_features = self.extract_phoneme_features(
                user_audio, 0, initial_duration
            )
            
            # 提取标准音频的声母特征
            standard_features = self.extract_phoneme_features(
                standard_audio, 0, initial_duration
            )
            
            if not user_features or not standard_features:
                return {'score': 0, 'error': '特征提取失败'}
            
            # 计算相似度
            similarity_scores = {}
            
            # 1. MFCC相似度
            if 'mfcc' in user_features and 'mfcc' in standard_features:
                mfcc_sim = 1 - cosine(user_features['mfcc'], standard_features['mfcc'])
                similarity_scores['mfcc'] = max(0, mfcc_sim)
            
            # 2. 梅尔频谱相似度
            if 'mel_mean' in user_features and 'mel_mean' in standard_features:
                mel_sim = 1 - cosine(user_features['mel_mean'], standard_features['mel_mean'])
                similarity_scores['mel'] = max(0, mel_sim)
            
            # 3. 频谱特征相似度
            spectral_features = ['spectral_centroid', 'spectral_bandwidth', 'zcr', 'energy']
            for feature in spectral_features:
                if feature in user_features and feature in standard_features:
                    # 计算相对误差
                    if standard_features[feature] != 0:
                        error = abs(user_features[feature] - standard_features[feature]) / standard_features[feature]
                        similarity_scores[feature] = max(0, 1 - error)
            
            # 4. 声母模板匹配
            template_score = self.match_initial_template(user_features, target_initial)
            similarity_scores['template'] = template_score
            
            # 综合评分
            if similarity_scores:
                # 加权平均
                weights = {
                    'mfcc': 0.3,
                    'mel': 0.25,
                    'spectral_centroid': 0.15,
                    'spectral_bandwidth': 0.1,
                    'zcr': 0.1,
                    'energy': 0.05,
                    'template': 0.05
                }
                
                total_score = 0
                total_weight = 0
                
                for feature, score in similarity_scores.items():
                    weight = weights.get(feature, 0)
                    if weight > 0:
                        total_score += score * weight
                        total_weight += weight
                
                final_score = (total_score / total_weight) * 100 if total_weight > 0 else 0
            else:
                final_score = 0
            
            result = {
                'score': min(100, max(0, final_score)),
                'similarity_scores': similarity_scores,
                'target_initial': target_initial,
                'user_features': user_features,
                'standard_features': standard_features,
                'feedback': self.generate_initial_feedback(similarity_scores, target_initial)
            }
            
            return result
            
        except Exception as e:
            print(f"声母评测失败: {e}")
            return {'score': 0, 'error': str(e)}
    
    def evaluate_final(self, user_audio: np.ndarray,
                      standard_audio: np.ndarray, 
                      target_final: str,
                      syllable_duration: float = 0.5) -> Dict[str, Any]:
        """
        评测韵母发音
        
        Args:
            user_audio: 用户录音
            standard_audio: 标准录音
            target_final: 目标韵母
            syllable_duration: 音节持续时间
            
        Returns:
            Dict[str, Any]: 韵母评测结果
        """
        try:
            # 提取韵母部分（从30%到100%的音节时间）
            start_time = syllable_duration * 0.3
            
            # 提取用户音频的韵母特征
            user_features = self.extract_phoneme_features(
                user_audio, start_time, syllable_duration
            )
            
            # 提取标准音频的韵母特征
            standard_features = self.extract_phoneme_features(
                standard_audio, start_time, syllable_duration
            )
            
            if not user_features or not standard_features:
                return {'score': 0, 'error': '特征提取失败'}
            
            # 计算相似度
            similarity_scores = {}
            
            # 1. 共振峰比较
            formant_score = self.compare_formants(
                user_features.get('formants', []),
                standard_features.get('formants', []),
                target_final
            )
            similarity_scores['formants'] = formant_score
            
            # 2. MFCC相似度
            if 'mfcc' in user_features and 'mfcc' in standard_features:
                mfcc_sim = 1 - cosine(user_features['mfcc'], standard_features['mfcc'])
                similarity_scores['mfcc'] = max(0, mfcc_sim)
            
            # 3. 频谱特征相似度
            spectral_features = ['spectral_centroid', 'spectral_bandwidth']
            for feature in spectral_features:
                if feature in user_features and feature in standard_features:
                    if standard_features[feature] != 0:
                        error = abs(user_features[feature] - standard_features[feature]) / standard_features[feature]
                        similarity_scores[feature] = max(0, 1 - error)
            
            # 4. 韵母模板匹配
            template_score = self.match_final_template(user_features, target_final)
            similarity_scores['template'] = template_score
            
            # 综合评分
            if similarity_scores:
                weights = {
                    'formants': 0.4,
                    'mfcc': 0.3,
                    'spectral_centroid': 0.15,
                    'spectral_bandwidth': 0.1,
                    'template': 0.05
                }
                
                total_score = 0
                total_weight = 0
                
                for feature, score in similarity_scores.items():
                    weight = weights.get(feature, 0)
                    if weight > 0:
                        total_score += score * weight
                        total_weight += weight
                
                final_score = (total_score / total_weight) * 100 if total_weight > 0 else 0
            else:
                final_score = 0
            
            result = {
                'score': min(100, max(0, final_score)),
                'similarity_scores': similarity_scores,
                'target_final': target_final,
                'user_features': user_features,
                'standard_features': standard_features,
                'feedback': self.generate_final_feedback(similarity_scores, target_final)
            }
            
            return result
            
        except Exception as e:
            print(f"韵母评测失败: {e}")
            return {'score': 0, 'error': str(e)}
    
    def compare_formants(self, user_formants: List[float], 
                        standard_formants: List[float],
                        target_final: str) -> float:
        """
        比较共振峰相似度
        
        Args:
            user_formants: 用户共振峰
            standard_formants: 标准共振峰
            target_final: 目标韵母
            
        Returns:
            float: 相似度分数 (0-1)
        """
        try:
            if not user_formants or not standard_formants:
                return 0.0
            
            # 确保两个列表长度相同
            min_len = min(len(user_formants), len(standard_formants))
            user_formants = user_formants[:min_len]
            standard_formants = standard_formants[:min_len]
            
            if min_len == 0:
                return 0.0
            
            # 计算每个共振峰的相似度
            formant_similarities = []
            
            for i in range(min_len):
                if standard_formants[i] == 0:
                    continue
                
                # 计算相对误差
                error = abs(user_formants[i] - standard_formants[i]) / standard_formants[i]
                
                # 转换为相似度 (容忍度为20%)
                similarity = max(0, 1 - error / 0.2)
                formant_similarities.append(similarity)
            
            # 返回平均相似度
            return np.mean(formant_similarities) if formant_similarities else 0.0
            
        except Exception as e:
            print(f"共振峰比较失败: {e}")
            return 0.0
    
    def match_initial_template(self, features: Dict[str, Any], initial: str) -> float:
        """
        与声母模板匹配
        
        Args:
            features: 声学特征
            initial: 声母
            
        Returns:
            float: 匹配分数 (0-1)
        """
        try:
            if initial not in self.initial_templates:
                return 0.5  # 默认分数
            
            template = self.initial_templates[initial]
            score = 0.5  # 基础分数
            
            # 根据能量模式调整分数
            energy = features.get('energy', 0)
            zcr = features.get('zcr', 0)
            
            if template['energy_pattern'] == 'burst':
                # 塞音应该有高能量和低零交叉率
                if energy > 0.01 and zcr < 0.1:
                    score += 0.3
            elif template['energy_pattern'] == 'fricative':
                # 擦音应该有高零交叉率
                if zcr > 0.1:
                    score += 0.3
            elif template['energy_pattern'] == 'nasal':
                # 鼻音应该有中等能量
                if 0.005 < energy < 0.02:
                    score += 0.3
            
            return min(1.0, score)
            
        except Exception as e:
            print(f"声母模板匹配失败: {e}")
            return 0.5
    
    def match_final_template(self, features: Dict[str, Any], final: str) -> float:
        """
        与韵母模板匹配
        
        Args:
            features: 声学特征
            final: 韵母
            
        Returns:
            float: 匹配分数 (0-1)
        """
        try:
            if final not in self.final_templates:
                return 0.5  # 默认分数
            
            template = self.final_templates[final]
            formants = features.get('formants', [])
            
            if not formants or len(formants) < 2:
                return 0.3
            
            # 比较前两个共振峰
            f1_target = template.get('f1', 0)
            f2_target = template.get('f2', 0)
            
            if isinstance(f1_target, list):
                # 复韵母，比较起始值
                f1_target = f1_target[0]
                f2_target = f2_target[0]
            
            score = 0.5
            
            # F1比较
            if f1_target > 0 and formants[0] > 0:
                f1_error = abs(formants[0] - f1_target) / f1_target
                if f1_error < 0.3:
                    score += 0.2
            
            # F2比较
            if f2_target > 0 and len(formants) > 1 and formants[1] > 0:
                f2_error = abs(formants[1] - f2_target) / f2_target
                if f2_error < 0.3:
                    score += 0.3
            
            return min(1.0, score)
            
        except Exception as e:
            print(f"韵母模板匹配失败: {e}")
            return 0.5
    
    def generate_initial_feedback(self, similarity_scores: Dict[str, float], initial: str) -> str:
        """
        生成声母评测反馈
        
        Args:
            similarity_scores: 相似度分数
            initial: 声母
            
        Returns:
            str: 反馈文本
        """
        try:
            feedback = []
            
            avg_score = np.mean(list(similarity_scores.values()))
            
            if avg_score > 0.8:
                feedback.append(f"声母 '{initial}' 发音准确！")
            elif avg_score > 0.6:
                feedback.append(f"声母 '{initial}' 发音基本正确，可以进一步改进。")
            else:
                feedback.append(f"声母 '{initial}' 发音需要改进。")
            
            # 具体建议
            if initial in ['zh', 'ch', 'sh']:
                feedback.append("注意翘舌音的舌位，舌尖要卷起。")
            elif initial in ['z', 'c', 's']:
                feedback.append("注意平舌音与翘舌音的区别。")
            elif initial in ['j', 'q', 'x']:
                feedback.append("注意舌面音的发音位置。")
            elif initial in ['b', 'p']:
                feedback.append("注意双唇音的送气区别。")
            elif initial in ['d', 't']:
                feedback.append("注意舌尖音的送气区别。")
            
            # 根据具体得分给出建议
            if similarity_scores.get('energy', 0) < 0.5:
                feedback.append("发音力度需要加强。")
            
            if similarity_scores.get('zcr', 0) < 0.5 and initial in ['f', 'h', 'x', 'sh', 's']:
                feedback.append("擦音的摩擦特征不够明显。")
            
            return " ".join(feedback)
            
        except Exception as e:
            print(f"生成声母反馈失败: {e}")
            return "无法生成详细反馈。"
    
    def generate_final_feedback(self, similarity_scores: Dict[str, float], final: str) -> str:
        """
        生成韵母评测反馈
        
        Args:
            similarity_scores: 相似度分数
            final: 韵母
            
        Returns:
            str: 反馈文本
        """
        try:
            feedback = []
            
            avg_score = np.mean(list(similarity_scores.values()))
            
            if avg_score > 0.8:
                feedback.append(f"韵母 '{final}' 发音准确！")
            elif avg_score > 0.6:
                feedback.append(f"韵母 '{final}' 发音基本正确，可以进一步改进。")
            else:
                feedback.append(f"韵母 '{final}' 发音需要改进。")
            
            # 具体建议
            if final in ['ai', 'ei', 'ao', 'ou']:
                feedback.append("注意复韵母的动程变化。")
            elif final in ['an', 'en', 'in', 'un']:
                feedback.append("注意前鼻音的舌位。")
            elif final in ['ang', 'eng', 'ing', 'ong']:
                feedback.append("注意后鼻音的发音部位。")
            elif final in ['v', 've']:
                feedback.append("注意撮口音的唇形。")
            
            # 根据共振峰得分给出建议
            formant_score = similarity_scores.get('formants', 0)
            if formant_score < 0.5:
                feedback.append("口形和舌位需要调整。")
            
            return " ".join(feedback)
            
        except Exception as e:
            print(f"生成韵母反馈失败: {e}")
            return "无法生成详细反馈。"

# 测试代码
if __name__ == "__main__":
    evaluator = PhonemeEvaluator()
    
    # 创建测试音频
    import librosa
    test_audio_path = "/mnt/e/Code/Outsourcing/普通话测评/mandarin_evaluation/data/音频/01.MP3"
    
    try:
        audio_data, sr = librosa.load(test_audio_path, sr=16000)
        print(f"加载测试音频: {len(audio_data)/sr:.2f}秒")
        
        # 测试特征提取
        features = evaluator.extract_phoneme_features(audio_data[:8000])  # 前0.5秒
        print(f"提取特征: {list(features.keys())}")
        
        # 测试声母评测
        initial_result = evaluator.evaluate_initial(
            audio_data[:8000], 
            audio_data[:8000], 
            'zh', 0.5
        )
        print(f"声母评测结果: {initial_result['score']:.1f}%")
        
        # 测试韵母评测
        final_result = evaluator.evaluate_final(
            audio_data[:8000],
            audio_data[:8000],
            'ao', 0.5
        )
        print(f"韵母评测结果: {final_result['score']:.1f}%")
        
    except Exception as e:
        print(f"测试失败: {e}")