# 普通话评测系统

基于声学特征分析的普通话发音评测系统，支持声母、韵母、声调、流畅性四个维度的评测。

## 功能特点

- 🎙️ **实时录音评测** - 支持录音功能，实时评测发音
- 📊 **四维度评分** - 声母、韵母、声调、流畅性全面评测
- 🎯 **错误定位** - 精确定位发音错误，提供改进建议
- 🖥️ **简洁GUI界面** - 操作简单直观的桌面应用
- 📈 **详细反馈** - 评测结果可视化展示，包含等级评定
- 📁 **音频管理** - 支持录音保存和音频文件加载

## 评测维度

### 1. 声母评测
- 基于频谱特征分析
- 支持平翘舌音、送气音等区分
- 提供具体的发音指导

### 2. 韵母评测  
- 共振峰频率对比分析
- 支持单韵母、复韵母、鼻韵母
- 口形和舌位准确度评估

### 3. 声调评测
- 基频轮廓匹配分析
- 四声调准确度评估
- 声调变化趋势对比

### 4. 流畅性评测
- 语速检测和分析
- 停顿合理性评估
- 语言节奏一致性评测

## 系统要求

- **操作系统**: Windows 10/11, macOS 10.14+, Linux
- **Python版本**: 3.7或更高版本
- **内存**: 建议4GB以上
- **存储**: 至少500MB可用空间
- **音频设备**: 支持录音的麦克风

## 安装指南

### 1. 环境准备

确保已安装Python 3.7+：
```bash
python --version
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

如果遇到安装问题，可以尝试：
```bash
# 使用清华源加速
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者逐个安装
pip install librosa soundfile pyaudio numpy scipy matplotlib pypinyin
```

### 3. 数据准备

确保以下数据文件存在：
- `data/音频/` - 标准朗读音频文件（01.MP3-50.MP3）
- `data/parsed_texts.json` - 文本数据文件

## 使用方法

### 快速启动

```bash
# 直接启动
python start.py

# 带系统测试启动
python start.py --test
```

或者直接运行主程序：
```bash
python main.py
```

### 基本操作流程

1. **选择文本** - 从下拉框中选择要朗读的文本
2. **开始录音** - 点击"开始录音"按钮，朗读所选文本
3. **停止录音** - 朗读完成后点击"停止录音"
4. **开始评测** - 点击"开始评测"按钮进行发音分析
5. **查看结果** - 在右侧查看详细的评测结果和建议

### 高级功能

- **播放标准音频** - 可以先听标准朗读作为参考
- **加载音频文件** - 支持加载已有的音频文件进行评测
- **保存录音** - 将录音保存为WAV文件
- **播放录音** - 回放自己的录音

## 评分标准

### 总分计算
- 声母：25%
- 韵母：25%  
- 声调：25%
- 流畅性：25%

### 等级划分
- **一级甲等**：97-100分
- **一级乙等**：92-96分
- **二级甲等**：87-91分
- **二级乙等**：80-86分
- **三级甲等**：70-79分
- **三级乙等**：60-69分
- **不合格**：60分以下

## 技术架构

### 核心模块

```
mandarin_evaluation/
├── main.py              # 主程序入口
├── start.py            # 启动脚本
├── test_system.py      # 系统测试
├── gui/                # GUI界面模块
│   └── main_window.py  # 主窗口
├── core/               # 核心评测算法
│   ├── audio_processor.py    # 音频处理
│   ├── text_processor.py     # 文本处理  
│   ├── phoneme_evaluator.py  # 声母韵母评测
│   ├── tone_evaluator.py     # 声调评测
│   ├── fluency_evaluator.py  # 流畅性评测
│   └── evaluator.py          # 主评测引擎
├── data/               # 数据文件
│   ├── 音频/           # 标准音频
│   └── parsed_texts.json    # 文本数据
└── models/             # 声学模型
```

### 算法原理

1. **声学特征提取** - 使用MFCC、梅尔频谱、共振峰等特征
2. **模式匹配** - 与标准音频进行DTW对齐和相似度计算
3. **模板对比** - 基于声学知识构建的发音模板
4. **统计分析** - 多维度特征的统计学评估

## 常见问题

### 1. 安装问题

**Q: pyaudio安装失败怎么办？**
A: 
- Windows: 下载对应版本的wheel文件安装
- macOS: `brew install portaudio && pip install pyaudio`
- Linux: `sudo apt-get install portaudio19-dev && pip install pyaudio`

**Q: librosa安装很慢？**
A: 使用国内镜像源：`pip install librosa -i https://pypi.tuna.tsinghua.edu.cn/simple/`

### 2. 运行问题

**Q: 录音没有声音？**
A: 检查麦克风权限和音频设备设置

**Q: 评测结果不准确？**
A: 确保录音质量良好，环境安静，发音清晰

**Q: 找不到标准音频文件？**
A: 确认`data/音频/`目录下有对应的MP3文件

### 3. 使用问题

**Q: 如何提高评测准确性？**
A: 
- 使用质量好的麦克风
- 在安静环境中录音
- 发音清晰，语速适中
- 按照标准普通话发音

## 系统测试

运行完整的系统测试：
```bash
python test_system.py
```

测试内容包括：
- 数据文件完整性检查
- 各模块功能测试
- 音频处理能力测试
- 评测算法准确性测试

## 开发说明

### 扩展功能
- 添加新的评测维度
- 支持更多音频格式
- 集成在线语音识别API
- 添加用户管理功能

### 性能优化
- 音频处理并行化
- 评测算法优化
- 内存使用优化
- GPU加速支持

## 更新日志

### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- ✨ 支持四维度评测
- 🖥️ GUI界面完善
- 📱 完整的用户交互体验

## 技术支持

如果遇到问题或有改进建议，请：

1. 查看本README的常见问题部分
2. 运行系统测试确认环境配置
3. 检查依赖包版本兼容性

## 许可证

本项目仅供学习和研究使用。

---

**普通话评测系统 v1.0** - 让普通话学习更科学、更高效！