# 错误标注报告功能使用说明

## 功能概述

新增的错误标注报告功能可以详细分析学员的发音错误，生成包含以下信息的详细报告：

- 具体哪个字读错了
- 错误类型（声母/韵母/声调）
- 标准读音 vs 检测读音
- 针对性的改进建议
- 发音统计和整体建议

## 使用方法

### 1. GUI界面使用

1. **完成评测**：先进行正常的录音和评测流程
2. **生成报告**：评测完成后，点击"📋 错误报告"按钮
3. **查看报告**：在弹出的窗口中查看详细的错误分析
4. **保存报告**：可以将报告保存为文本文件

### 2. 编程接口使用

```python
from core.evaluator import MandarinEvaluator

# 初始化评测器
evaluator = MandarinEvaluator()
evaluator.set_data_path("data")

# 进行完整评测
evaluation_result = evaluator.evaluate_complete(audio_data, text_id)

# 生成错误报告
error_report = evaluator.generate_error_report(evaluation_result)
print(error_report)
```

## 报告格式示例

```
==================================================
普通话评测详细错误报告
==================================================
文本：照北京的老规矩，春节差不多在腊月的初旬就开始了...
总分：82.5分 (二级乙等)

错误详情：
------------------------------
第1个字：照 [zhao4] (得分: 75.2)
  ⚠️ 声调错误 (65分)
    标准读音：第四声
    建议：第四声要从高音快速降到低音，降调要明显

第15个字：差 [cha4] (得分: 68.5)
  ❌ 声母错误 (58分)
    标准读音：ch
    建议：注意翘舌音的舌位，舌尖要卷起

发音统计：
------------------------------
总字数：128字
正确发音：115字 (89.8%)
需要改进：13字 (10.2%)
声母错误：5个
韵母错误：3个
声调错误：5个

改进建议：
------------------------------
1. 重点练习翘舌音zh、ch、sh的发音
2. 注意第四声的完整降调
3. 建议跟读标准音频加强练习

评测时间：2024-01-01 12:00:00
==================================================
```

## 错误判定标准

- **严重错误 ❌**：得分 < 60分
- **需要改进 ⚠️**：得分 60-75分  
- **基本正确 ✅**：得分 > 75分

## 错误类型说明

### 声母错误
- 平翘舌音混淆（z/zh, c/ch, s/sh）
- 送气音区别（b/p, d/t, g/k）
- 舌面音发音位置（j, q, x）

### 韵母错误
- 复韵母动程变化（ai, ei, ao, ou）
- 前后鼻音区别（an/ang, en/eng等）
- 撮口音唇形（ü, üe）

### 声调错误
- 四声调值不准确
- 轻声处理不当
- 声调变化趋势错误

## 测试功能

运行测试脚本验证功能：

```bash
python test_error_report.py
```

## 技术实现

- 基于现有的音节级别评测数据
- 通过分数阈值判定错误类型
- 结合拼音分析提供准确的标准读音
- 根据错误模式生成个性化建议

## 注意事项

1. 需要先完成完整评测才能生成错误报告
2. 报告基于声学特征分析，可能存在一定误差
3. 建议结合人工判断进行最终确认
4. 适用于普通话水平测试的50篇标准文本
