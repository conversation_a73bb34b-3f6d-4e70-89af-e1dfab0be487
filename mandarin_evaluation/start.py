#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话评测系统启动脚本
"""

import sys
import os
from pathlib import Path
import subprocess

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'numpy',
        'librosa', 
        'soundfile',
        'pyaudio',
        'scipy',
        'matplotlib',
        'pypinyin'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def run_tests():
    """运行系统测试"""
    print("正在运行系统测试...")
    try:
        # 运行测试脚本
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 系统测试通过")
            return True
        else:
            print("✗ 系统测试失败")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return False

def start_gui():
    """启动GUI应用"""
    try:
        print("启动普通话评测系统...")
        from main import main
        return main()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        return 1

def main():
    """主函数"""
    print("普通话评测系统 v1.0")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 询问是否运行测试
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        if not run_tests():
            return 1
    else:
        print("使用 --test 参数可以运行系统测试")
    
    # 启动GUI
    return start_gui()

if __name__ == "__main__":
    sys.exit(main())