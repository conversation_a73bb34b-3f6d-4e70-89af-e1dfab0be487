#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话评测系统 - 主程序入口
Mandarin Pronunciation Evaluation System
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from gui.main_window import MainWindow

def main():
    """主程序入口"""
    try:
        app = MainWindow()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        return 1
    return 0

if __name__ == "__main__":
    sys.exit(main())