#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
测试普通话评测系统的基本功能
"""

import sys
import os
from pathlib import Path
import numpy as np
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_audio_processor():
    """测试音频处理器"""
    print("=== 测试音频处理器 ===")
    try:
        from core.audio_processor import AudioProcessor
        
        processor = AudioProcessor()
        
        # 测试加载音频
        test_audio_path = project_root / "data" / "音频" / "01.MP3"
        if test_audio_path.exists():
            result = processor.load_audio(str(test_audio_path))
            if result:
                audio_data, sr = result
                print(f"✓ 音频加载成功: {len(audio_data)/sr:.2f}秒")
                
                # 测试特征提取
                features = processor.extract_features(audio_data[:8000])
                print(f"✓ 特征提取成功: {len(features)} 种特征")
                
                # 测试静音检测
                silence_segments = processor.detect_silence(audio_data)
                print(f"✓ 静音检测成功: {len(silence_segments)} 个静音段")
                
                return True
            else:
                print("✗ 音频加载失败")
                return False
        else:
            print(f"✗ 测试音频文件不存在: {test_audio_path}")
            return False
            
    except Exception as e:
        print(f"✗ 音频处理器测试失败: {e}")
        return False

def test_text_processor():
    """测试文本处理器"""
    print("\n=== 测试文本处理器 ===")
    try:
        from core.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 测试文本处理
        test_text = "春天来了，万物复苏。"
        
        # 拼音转换
        pinyins = processor.text_to_pinyin(test_text)
        print(f"✓ 拼音转换成功: {pinyins}")
        
        # 完整文本处理
        result = processor.process_text(test_text)
        if result:
            print(f"✓ 文本处理成功: {result['character_count']} 个字符")
            
            # 难度评估
            difficulty = processor.get_pronunciation_difficulty(test_text)
            print(f"✓ 难度评估成功: {difficulty['overall_difficulty']}")
            
            return True
        else:
            print("✗ 文本处理失败")
            return False
            
    except Exception as e:
        print(f"✗ 文本处理器测试失败: {e}")
        return False

def test_phoneme_evaluator():
    """测试声母韵母评测器"""
    print("\n=== 测试声母韵母评测器 ===")
    try:
        from core.phoneme_evaluator import PhonemeEvaluator
        from core.audio_processor import AudioProcessor
        
        evaluator = PhonemeEvaluator()
        processor = AudioProcessor()
        
        # 加载测试音频
        test_audio_path = project_root / "data" / "音频" / "01.MP3"
        if test_audio_path.exists():
            result = processor.load_audio(str(test_audio_path))
            if result:
                audio_data, sr = result
                test_segment = audio_data[:8000]  # 前0.5秒
                
                # 测试特征提取
                features = evaluator.extract_phoneme_features(test_segment)
                if features:
                    print(f"✓ 音素特征提取成功: {len(features)} 种特征")
                    
                    # 测试声母评测
                    consonant_result = evaluator.evaluate_initial(
                        test_segment, test_segment, 'zh', 0.5
                    )
                    print(f"✓ 声母评测成功: {consonant_result['score']:.1f}分")
                    
                    # 测试韵母评测
                    vowel_result = evaluator.evaluate_final(  
                        test_segment, test_segment, 'ao', 0.5
                    )
                    print(f"✓ 韵母评测成功: {vowel_result['score']:.1f}分")
                    
                    return True
                else:
                    print("✗ 音素特征提取失败")
                    return False
            else:
                print("✗ 音频加载失败")
                return False
        else:
            print("✗ 测试音频文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 声母韵母评测器测试失败: {e}")
        return False

def test_tone_evaluator():
    """测试声调评测器"""
    print("\n=== 测试声调评测器 ===")
    try:
        from core.tone_evaluator import ToneEvaluator
        from core.audio_processor import AudioProcessor
        
        evaluator = ToneEvaluator()
        processor = AudioProcessor()
        
        # 加载测试音频
        test_audio_path = project_root / "data" / "音频" / "01.MP3"
        if test_audio_path.exists():
            result = processor.load_audio(str(test_audio_path))
            if result:
                audio_data, sr = result
                test_segment = audio_data[:int(0.8*sr)]  # 前0.8秒
                
                # 测试基频提取
                f0_data = evaluator.extract_f0_contour(test_segment)
                if 'error' not in f0_data:
                    print(f"✓ 基频提取成功: 平均基频 {f0_data['statistics']['mean_f0']:.1f}Hz")
                    
                    # 测试声调评测
                    tone_result = evaluator.evaluate_tone(
                        test_segment, test_segment, 1, 0.8
                    )
                    if 'error' not in tone_result:
                        print(f"✓ 声调评测成功: {tone_result['score']:.1f}分")
                        return True
                    else:
                        print(f"✗ 声调评测失败: {tone_result['error']}")
                        return False
                else:
                    print(f"✗ 基频提取失败: {f0_data['error']}")
                    return False
            else:
                print("✗ 音频加载失败")
                return False
        else:
            print("✗ 测试音频文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 声调评测器测试失败: {e}")
        return False

def test_fluency_evaluator():
    """测试流畅性评测器"""
    print("\n=== 测试流畅性评测器 ===")
    try:
        from core.fluency_evaluator import FluencyEvaluator
        from core.audio_processor import AudioProcessor
        
        evaluator = FluencyEvaluator()
        processor = AudioProcessor()
        
        # 加载测试音频
        test_audio_path = project_root / "data" / "音频" / "01.MP3" 
        if test_audio_path.exists():
            result = processor.load_audio(str(test_audio_path))
            if result:
                audio_data, sr = result
                
                # 测试语音段检测
                segments = evaluator.detect_speech_segments(audio_data)
                if segments:
                    print(f"✓ 语音段检测成功: {len(segments['speech_segments'])} 个语音段")
                    
                    # 测试语速计算
                    speech_rate = evaluator.calculate_speech_rate(audio_data, 50)
                    if speech_rate:
                        print(f"✓ 语速计算成功: {speech_rate['overall_rate']:.2f} 音节/秒")
                        
                        # 测试流畅性评测
                        test_text = "这是一个测试文本，用来评测流畅性。"
                        fluency_result = evaluator.evaluate_fluency(
                            audio_data, audio_data, test_text, 50
                        )
                        if 'error' not in fluency_result:
                            print(f"✓ 流畅性评测成功: {fluency_result['score']:.1f}分")
                            return True
                        else:
                            print(f"✗ 流畅性评测失败: {fluency_result['error']}")
                            return False
                    else:
                        print("✗ 语速计算失败")
                        return False
                else:
                    print("✗ 语音段检测失败")
                    return False
            else:
                print("✗ 音频加载失败")
                return False
        else:
            print("✗ 测试音频文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 流畅性评测器测试失败: {e}")
        return False

def test_main_evaluator():
    """测试主评测引擎"""
    print("\n=== 测试主评测引擎 ===")
    try:
        from core.evaluator import MandarinEvaluator
        
        evaluator = MandarinEvaluator()
        
        # 设置数据路径
        data_path = project_root / "data"
        evaluator.set_data_path(str(data_path))
        
        # 测试加载标准音频
        result = evaluator.load_standard_audio("1")
        if result:
            audio_data, sr = result
            print(f"✓ 标准音频加载成功: {len(audio_data)/sr:.2f}秒")
            
            # 测试完整评测
            def progress_callback(message):
                print(f"  进度: {message}")
            
            evaluation_result = evaluator.evaluate_complete(
                audio_data, "1", progress_callback
            )
            
            if 'error' not in evaluation_result:
                print(f"✓ 完整评测成功")
                print(f"  总分: {evaluation_result['total_score']:.1f}分")
                print(f"  等级: {evaluation_result['grade']}")
                
                # 生成摘要
                summary = evaluator.get_evaluation_summary(evaluation_result)
                print("✓ 评测摘要生成成功")
                
                return True
            else:
                print(f"✗ 完整评测失败: {evaluation_result['error']}")
                return False
        else:
            print("✗ 标准音频加载失败")
            return False
            
    except Exception as e:
        print(f"✗ 主评测引擎测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件（不启动完整GUI）"""
    print("\n=== 测试GUI组件 ===")
    try:
        # 只导入和实例化，不运行
        from gui.main_window import MainWindow
        
        print("✓ GUI模块导入成功")
        
        # 可以测试其他非GUI相关的方法
        return True
        
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        return False

def check_data_files():
    """检查数据文件"""
    print("\n=== 检查数据文件 ===")
    
    data_dir = project_root / "data"
    audio_dir = data_dir / "音频"
    texts_file = data_dir / "parsed_texts.json"
    
    # 检查目录结构
    if not data_dir.exists():
        print("✗ data目录不存在")
        return False
    
    if not audio_dir.exists():
        print("✗ 音频目录不存在")
        return False
    
    if not texts_file.exists():
        print("✗ 文本数据文件不存在")
        return False
    
    # 检查音频文件
    audio_files = list(audio_dir.glob("*.MP3")) + list(audio_dir.glob("*.mp3"))
    print(f"✓ 找到 {len(audio_files)} 个音频文件")
    
    # 检查文本数据
    try:
        with open(texts_file, 'r', encoding='utf-8') as f:
            texts_data = json.load(f)
            print(f"✓ 加载了 {len(texts_data)} 个文本")
    except Exception as e:
        print(f"✗ 文本数据加载失败: {e}")
        return False
    
    return True

def run_all_tests():
    """运行所有测试"""
    print("普通话评测系统 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("数据文件检查", check_data_files),
        ("音频处理器", test_audio_processor),
        ("文本处理器", test_text_processor),
        ("声母韵母评测器", test_phoneme_evaluator),
        ("声调评测器", test_tone_evaluator),
        ("流畅性评测器", test_fluency_evaluator),
        ("主评测引擎", test_main_evaluator),
        ("GUI组件", test_gui_components),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)