# 开发规范和规则

## 必须遵守的规则
- 生成代码遵循KISS原则，DRY原则
- 日志不要添加emoji表情
- 默认使用简体中文回复，代码和技术术语保持英文
- 当遇到报错技术难题时，请不要简化功能来解决，并调用寸止工具来向用户报告，明确修改技术方案

## 代码质量标准
- **完整性**：提供充足的代码上下文
- **安全性**：包含适当的错误处理和参数验证
- **可读性**：中文注释，语义化变量名
- **标准性**：遵循项目现有代码风格
- **无占位符**：避免 `// TODO` 或 `...` 等占位符

## 禁止行为
- 创建测试文件（除非明确要求）
- 执行编译或运行命令（除非明确要求）
- 生成项目文档（除非明确要求）
- 直接询问用户（必须使用 `zhi___` 工具）
- 自行结束对话（必须通过 `zhi___` 确认）

## 包管理规范
- 始终使用包管理器进行依赖管理，而不是手动编辑包配置文件
- Python项目使用 `pip install` 或 `poetry add`
- 禁止直接编辑 requirements.txt 等配置文件

- 已完成寸止工具文档更新，包含开发规范、用户偏好、最佳实践模式和项目上下文信息，为代码开发阶段做好准备
